<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="28" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/> <!-- General permission -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC"/> <!-- Specific permission for dataSync type -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/> <!-- For installing APK files -->
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES"/> <!-- For managing packages -->

    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.egyfilm"
        android:networkSecurityConfig="@xml/network_security_config"
        android:hardwareAccelerated="true"
        tools:targetApi="m">


        <activity
            android:name=".activity.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.egyfilm.NoActionBar.Fullscreen">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".activity.MainActivity"
            android:configChanges="uiMode|keyboard|keyboardHidden|screenSize|screenLayout|smallestScreenSize|orientation"
            android:exported="true"
            android:launchMode="singleTop" /> <!-- Add launchMode singleTop -->

        <!-- Declare the Download Activity -->
        <activity
            android:name=".activity.DownloadActivity"
            android:exported="false"
            android:label="Downloads"
            android:parentActivityName=".activity.MainActivity"
            android:theme="@style/Theme.egyfilm"> <!-- Use the base theme which inherits NoActionBar -->
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <!-- Declare the Settings Activity -->
        <activity
            android:name=".activity.SettingsActivity"
            android:exported="false"
            android:label="@string/settings"
            android:parentActivityName=".activity.MainActivity"
            android:theme="@style/Theme.egyfilm">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <!-- Declare the Filter Updates Activity -->
        <activity
            android:name=".activity.FilterUpdatesActivity"
            android:exported="false"
            android:label="@string/filter_updates"
            android:parentActivityName=".activity.SettingsActivity"
            android:theme="@style/Theme.egyfilm">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <!-- Declare the Download Service -->
        <service
            android:name=".service.DownloadService"
            android:exported="false"
            android:foregroundServiceType="dataSync" />

        <!-- Firebase Messaging Service -->
        <service
            android:name=".service.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Firebase default notification channel -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="egyfilm_notifications" />

        <!-- FileProvider Declaration -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>

    </application>

</manifest>

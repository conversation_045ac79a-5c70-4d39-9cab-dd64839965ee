package com.elewashy.egyfilm.activity

import android.content.*
import android.net.Uri
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AlertDialog
import androidx.core.content.FileProvider
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.elewashy.egyfilm.adapter.DownloadAdapter
import com.elewashy.egyfilm.databinding.ActivityDownloadBinding
import com.elewashy.egyfilm.model.DownloadItem
import com.elewashy.egyfilm.model.DownloadStatus
import com.elewashy.egyfilm.service.DownloadService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File
import android.content.ActivityNotFoundException
import android.content.pm.PackageManager
import android.os.Build
import android.os.Environment
import android.provider.Settings
import com.elewashy.egyfilm.util.PermissionManager

class DownloadActivity : AppCompatActivity() {

    private lateinit var binding: ActivityDownloadBinding
    private lateinit var downloadAdapter: DownloadAdapter
    private var downloadService: DownloadService? = null
    private var isBound = false
    private lateinit var permissionManager: PermissionManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDownloadBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize permission manager
        permissionManager = PermissionManager(this)

        setSupportActionBar(binding.toolbarDownloads)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        supportActionBar?.title = "Downloads"

        setupRecyclerView()
    }

    // Function to open the downloaded file with enhanced APK handling
    private fun openDownloadedFile(item: DownloadItem) {
        val file = File(item.filePath)
        if (!file.exists()) {
            Toast.makeText(this, "Error: File not found", Toast.LENGTH_SHORT).show()
            return
        }

        // Check if it's an APK file
        if (item.fileName.endsWith(".apk", ignoreCase = true)) {
            installApkFile(file)
            return
        }

        val fileUri: Uri? = try {
            FileProvider.getUriForFile(
                this,
                "${applicationContext.packageName}.provider",
                file
            )
        } catch (e: IllegalArgumentException) {
            Toast.makeText(this, "Error opening file: Cannot create URI", Toast.LENGTH_SHORT).show()
            null
        }

        fileUri?.let { uri ->
            val mime = item.mimeType ?: contentResolver.getType(uri) ?: "*/*"
            val viewIntent = Intent(Intent.ACTION_VIEW).apply {
                setDataAndType(uri, mime)
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
            try {
                startActivity(viewIntent)
            } catch (e: ActivityNotFoundException) {
                Toast.makeText(this, "No application found to open this file type ($mime)", Toast.LENGTH_LONG).show()
            } catch (e: Exception) {
                Toast.makeText(this, "Error opening file", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // Function to install APK files
    private fun installApkFile(file: File) {
        // Use PermissionManager for install packages permission
        permissionManager.requestInstallPackagesPermission(
            onGranted = {
                // Permission granted, proceed with installation
                try {
                    val fileUri = FileProvider.getUriForFile(
                        this,
                        "${applicationContext.packageName}.provider",
                        file
                    )

                    val installIntent = Intent(Intent.ACTION_VIEW).apply {
                        setDataAndType(fileUri, "application/vnd.android.package-archive")
                        addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    
                    startActivity(installIntent)
                } catch (e: Exception) {
                    Toast.makeText(this, "Error installing APK: ${e.message}", Toast.LENGTH_LONG).show()
                }
            },
            onDenied = {
                Toast.makeText(this, "Cannot install APK without permission.", Toast.LENGTH_SHORT).show()
            }
        )
    }

    // Enhanced function to delete the downloaded file
    private fun deleteDownloadedFile(item: DownloadItem) {
        // Check storage permission first
        permissionManager.requestStoragePermission(
            onGranted = {
                // Permission granted, show delete confirmation
                AlertDialog.Builder(this)
                    .setTitle("Delete File")
                    .setMessage("Are you sure you want to delete '${item.fileName}' from your device?")
                    .setPositiveButton("Delete") { _, _ ->
                        deleteFileFromDevice(item)
                    }
                    .setNegativeButton("Cancel", null)
                    .show()
            },
            onDenied = {
                Toast.makeText(this, "Storage permission is required to delete files", Toast.LENGTH_LONG).show()
            }
        )
    }

    private fun deleteFileFromDevice(item: DownloadItem) {
        try {
            val file = File(item.filePath)
            var deleted = false
            
            if (file.exists()) {
                // Try to delete the file
                deleted = file.delete()
                
                if (!deleted) {
                    // If normal delete fails, try alternative methods
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R && Environment.isExternalStorageManager()) {
                        // Use more aggressive deletion for Android 11+
                        deleted = file.delete()
                    }
                }
            } else {
                // File doesn't exist, consider it "deleted"
                deleted = true
            }

            if (deleted) {
                Toast.makeText(this, "File deleted successfully from device", Toast.LENGTH_SHORT).show()
                // Remove from download service
                sendControlIntent(DownloadService.ACTION_CANCEL_DOWNLOAD, item.id)
                
                // Also try to delete parent directory if it's empty
                try {
                    val parentDir = file.parentFile
                    if (parentDir != null && parentDir.exists() && parentDir.listFiles()?.isEmpty() == true) {
                        parentDir.delete()
                    }
                } catch (e: Exception) {
                    // Ignore parent directory deletion errors
                }
            } else {
                Toast.makeText(this, "Failed to delete file from device. Check app permissions.", Toast.LENGTH_LONG).show()
                
                // Still remove from list even if file deletion failed
                AlertDialog.Builder(this)
                    .setTitle("Remove from List")
                    .setMessage("File could not be deleted. Remove from download list anyway?")
                    .setPositiveButton("Remove") { _, _ ->
                        sendControlIntent(DownloadService.ACTION_CANCEL_DOWNLOAD, item.id)
                        Toast.makeText(this, "Removed from download list", Toast.LENGTH_SHORT).show()
                    }
                    .setNegativeButton("Cancel", null)
                    .show()
            }
        } catch (e: Exception) {
            Toast.makeText(this, "Error deleting file: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    // Define the click listener lambdas
    private val onDownloadItemClicked: (DownloadItem) -> Unit = { item ->
        if (item.status == DownloadStatus.COMPLETED) {
            openDownloadedFile(item)
        }
    }

    private val onOpenClicked: (DownloadItem) -> Unit = { item ->
        openDownloadedFile(item)
    }

    private val onDeleteClicked: (DownloadItem) -> Unit = { item ->
        deleteDownloadedFile(item)
    }

    private val connection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            val binder = service as DownloadService.DownloadBinder
            downloadService = binder.getService()
            isBound = true
            loadDownloads() // Load initial data
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            isBound = false
            downloadService = null
        }
    }

    override fun onStart() {
        super.onStart()
        Intent(this, DownloadService::class.java).also { intent ->
            bindService(intent, connection, Context.BIND_AUTO_CREATE)
        }
        LocalBroadcastManager.getInstance(this).registerReceiver(
            downloadUpdateReceiver,
            IntentFilter(DownloadService.ACTION_DOWNLOAD_UPDATE)
        )
    }

    override fun onStop() {
        super.onStop()
        if (isBound) {
            unbindService(connection)
            isBound = false
        }
        LocalBroadcastManager.getInstance(this).unregisterReceiver(downloadUpdateReceiver)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun setupRecyclerView() {
        downloadAdapter = DownloadAdapter(
            context = this,
            onPauseClick = { item -> sendControlIntent(DownloadService.ACTION_PAUSE_DOWNLOAD, item.id) },
            onResumeClick = { item -> sendControlIntent(DownloadService.ACTION_RESUME_DOWNLOAD, item.id) },
            onCancelClick = { item -> sendControlIntent(DownloadService.ACTION_CANCEL_DOWNLOAD, item.id) },
            onRetryClick = { item ->
                sendControlIntent(DownloadService.ACTION_CANCEL_DOWNLOAD, item.id)
                lifecycleScope.launch {
                    delay(200)
                    val startIntent = DownloadService.createStartIntent(
                        context = this@DownloadActivity,
                        url = item.url,
                        fileName = item.fileName,
                        mimeType = item.mimeType,
                        userAgent = item.userAgent,
                        referer = item.referer,
                        origin = item.origin,
                        cookies = item.cookies,
                        source = "RETRY"
                    )
                    startService(startIntent)
                }
            },
            onItemClick = onDownloadItemClicked,
            onOpenClick = onOpenClicked,
            onDeleteClick = onDeleteClicked
        )

        binding.rvDownloads.apply {
            adapter = downloadAdapter
            layoutManager = LinearLayoutManager(this@DownloadActivity)
            setHasFixedSize(true)
        }
    }

    private fun loadDownloads() {
        if (!isBound) {
            return
        }
        val currentService = downloadService
        if (currentService == null) {
            updateDownloadList(emptyList())
            return
        }
        val items = currentService.getDownloadItems()
        updateDownloadList(items)
    }

    private fun updateDownloadList(downloads: List<DownloadItem>) {
        downloadAdapter.submitList(downloads)
        binding.layoutNoDownloads.isVisible = downloads.isEmpty()
        binding.rvDownloads.isVisible = downloads.isNotEmpty()
    }

    private fun sendControlIntent(action: String, downloadId: Long) {
        if (!isBound || downloadService == null) {
            return
        }
        val intent = DownloadService.createControlIntent(this, action, downloadId)
        startService(intent)
    }

    private val downloadUpdateReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == DownloadService.ACTION_DOWNLOAD_UPDATE) {
                val listChanged = intent.getBooleanExtra(DownloadService.EXTRA_DOWNLOAD_LIST_CHANGED, false)
                lifecycleScope.launch(Dispatchers.Main) {
                    if (isBound && downloadService != null) {
                        loadDownloads()
                    }
                }
            }
        }
    }
}

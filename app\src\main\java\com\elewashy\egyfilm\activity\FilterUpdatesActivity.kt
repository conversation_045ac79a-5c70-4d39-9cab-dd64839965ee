package com.elewashy.egyfilm.activity

import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.databinding.ActivityFilterUpdatesBinding
import com.elewashy.egyfilm.fragment.AdBlocker
import com.elewashy.egyfilm.fragment.ValidLinkChecker
import com.elewashy.egyfilm.fragment.OpenLinkValidator
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*

class FilterUpdatesActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityFilterUpdatesBinding
    private lateinit var adBlocker: AdBlocker
    private lateinit var validLinkChecker: ValidLinkChecker
    private lateinit var openLinkValidator: OpenLinkValidator
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFilterUpdatesBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Initialize filter instances
        adBlocker = AdBlocker.getInstance(this)
        validLinkChecker = ValidLinkChecker.getInstance(this)
        openLinkValidator = OpenLinkValidator.getInstance(this)
        
        setupUI()
        loadLastUpdatedTimes()
    }
    
    private fun setupUI() {
        // Back button
        binding.backButton.setOnClickListener {
            finish()
        }
        
        // Hide status texts initially
        binding.adBlockerStatus.visibility = View.GONE
        binding.validLinksStatus.visibility = View.GONE
        binding.openLinksStatus.visibility = View.GONE
        
        // Individual filter update buttons
        binding.updateAdBlockerBtn.setOnClickListener {
            updateAdBlockerFilter()
        }
        
        binding.updateValidLinksBtn.setOnClickListener {
            updateValidLinksFilter()
        }
        
        binding.updateOpenLinksBtn.setOnClickListener {
            updateOpenLinksFilter()
        }
        
        // Update all filters button
        binding.updateAllFiltersBtn.setOnClickListener {
            updateAllFilters()
        }
    }
    
    private fun loadLastUpdatedTimes() {
        val sharedPrefs = getSharedPreferences("FilterUpdateTimes", MODE_PRIVATE)
        val dateFormat = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
        
        // Load AdBlocker update times
        val adBlockerTime = sharedPrefs.getLong("adBlockerLastUpdate", 0)
        val adBlockerFrequentTime = sharedPrefs.getLong("adBlockerFrequentLastUpdate", 0)
        val latestAdBlockerTime = maxOf(adBlockerTime, adBlockerFrequentTime)
        
        binding.adBlockerLastUpdate.text = if (latestAdBlockerTime > 0) {
            getString(R.string.last_updated, dateFormat.format(Date(latestAdBlockerTime)))
        } else {
            getString(R.string.never_updated)
        }
        
        // Load ValidLinks update time
        val validLinksTime = sharedPrefs.getLong("validLinksLastUpdate", 0)
        binding.validLinksLastUpdate.text = if (validLinksTime > 0) {
            getString(R.string.last_updated, dateFormat.format(Date(validLinksTime)))
        } else {
            getString(R.string.never_updated)
        }
        
        // Load OpenLinks update time
        val openLinksTime = sharedPrefs.getLong("openLinksLastUpdate", 0)
        binding.openLinksLastUpdate.text = if (openLinksTime > 0) {
            getString(R.string.last_updated, dateFormat.format(Date(openLinksTime)))
        } else {
            getString(R.string.never_updated)
        }
    }
    
    private fun updateAdBlockerFilter() {
        binding.updateAdBlockerBtn.isEnabled = false
        binding.adBlockerProgress.visibility = View.VISIBLE
        binding.adBlockerStatus.visibility = View.VISIBLE
        binding.adBlockerStatus.text = getString(R.string.updating)
        
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                // Force update by clearing time constraints temporarily
                clearAdBlockerTimeConstraints()
                
                // Update both lists
                adBlocker.updateEasyList()
                adBlocker.updateFrequentList()
                
                // Save update time
                saveUpdateTime("adBlockerLastUpdate")
                
                withContext(Dispatchers.Main) {
                    binding.adBlockerStatus.text = getString(R.string.update_success)
                    Toast.makeText(this@FilterUpdatesActivity, "AdBlocker Filter ${getString(R.string.update_success)}", Toast.LENGTH_SHORT).show()
                    loadLastUpdatedTimes()
                }
                
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.adBlockerStatus.text = getString(R.string.update_failed)
                    Toast.makeText(this@FilterUpdatesActivity, "AdBlocker Filter ${getString(R.string.update_failed)}", Toast.LENGTH_SHORT).show()
                }
            } finally {
                withContext(Dispatchers.Main) {
                    binding.updateAdBlockerBtn.isEnabled = true
                    binding.adBlockerProgress.visibility = View.GONE
                }
            }
        }
    }
    
    private fun updateValidLinksFilter() {
        binding.updateValidLinksBtn.isEnabled = false
        binding.validLinksProgress.visibility = View.VISIBLE
        binding.validLinksStatus.visibility = View.VISIBLE
        binding.validLinksStatus.text = getString(R.string.updating)
        
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                validLinkChecker.updateValidLinks()
                saveUpdateTime("validLinksLastUpdate")
                
                withContext(Dispatchers.Main) {
                    binding.validLinksStatus.text = getString(R.string.update_success)
                    Toast.makeText(this@FilterUpdatesActivity, "Valid Links Filter ${getString(R.string.update_success)}", Toast.LENGTH_SHORT).show()
                    loadLastUpdatedTimes()
                }
                
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.validLinksStatus.text = getString(R.string.update_failed)
                    Toast.makeText(this@FilterUpdatesActivity, "Valid Links Filter ${getString(R.string.update_failed)}", Toast.LENGTH_SHORT).show()
                }
            } finally {
                withContext(Dispatchers.Main) {
                    binding.updateValidLinksBtn.isEnabled = true
                    binding.validLinksProgress.visibility = View.GONE
                }
            }
        }
    }
    
    private fun updateOpenLinksFilter() {
        binding.updateOpenLinksBtn.isEnabled = false
        binding.openLinksProgress.visibility = View.VISIBLE
        binding.openLinksStatus.visibility = View.VISIBLE
        binding.openLinksStatus.text = getString(R.string.updating)
        
        lifecycleScope.launch(Dispatchers.IO) {
            try {
                openLinkValidator.updateOpenLinks()
                saveUpdateTime("openLinksLastUpdate")
                
                withContext(Dispatchers.Main) {
                    binding.openLinksStatus.text = getString(R.string.update_success)
                    Toast.makeText(this@FilterUpdatesActivity, "Open Links Filter ${getString(R.string.update_success)}", Toast.LENGTH_SHORT).show()
                    loadLastUpdatedTimes()
                }
                
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.openLinksStatus.text = getString(R.string.update_failed)
                    Toast.makeText(this@FilterUpdatesActivity, "Open Links Filter ${getString(R.string.update_failed)}", Toast.LENGTH_SHORT).show()
                }
            } finally {
                withContext(Dispatchers.Main) {
                    binding.updateOpenLinksBtn.isEnabled = true
                    binding.openLinksProgress.visibility = View.GONE
                }
            }
        }
    }
    
    private fun updateAllFilters() {
        binding.updateAllFiltersBtn.isEnabled = false
        binding.updateAllFiltersBtn.text = getString(R.string.updating)
        
        // Show all progress indicators and status texts
        binding.adBlockerProgress.visibility = View.VISIBLE
        binding.validLinksProgress.visibility = View.VISIBLE
        binding.openLinksProgress.visibility = View.VISIBLE
        
        binding.adBlockerStatus.visibility = View.VISIBLE
        binding.validLinksStatus.visibility = View.VISIBLE
        binding.openLinksStatus.visibility = View.VISIBLE
        
        // Update all status texts
        binding.adBlockerStatus.text = getString(R.string.updating)
        binding.validLinksStatus.text = getString(R.string.updating)
        binding.openLinksStatus.text = getString(R.string.updating)
        
        lifecycleScope.launch(Dispatchers.IO) {
            var successCount = 0
            var totalCount = 3
            
            // Update AdBlocker
            try {
                clearAdBlockerTimeConstraints()
                adBlocker.updateEasyList()
                adBlocker.updateFrequentList()
                saveUpdateTime("adBlockerLastUpdate")
                withContext(Dispatchers.Main) {
                    binding.adBlockerStatus.text = getString(R.string.update_success)
                }
                successCount++
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.adBlockerStatus.text = getString(R.string.update_failed)
                }
            }
            
            // Update ValidLinks
            try {
                validLinkChecker.updateValidLinks()
                saveUpdateTime("validLinksLastUpdate")
                withContext(Dispatchers.Main) {
                    binding.validLinksStatus.text = getString(R.string.update_success)
                }
                successCount++
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.validLinksStatus.text = getString(R.string.update_failed)
                }
            }
            
            // Update OpenLinks
            try {
                openLinkValidator.updateOpenLinks()
                saveUpdateTime("openLinksLastUpdate")
                withContext(Dispatchers.Main) {
                    binding.openLinksStatus.text = getString(R.string.update_success)
                }
                successCount++
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.openLinksStatus.text = getString(R.string.update_failed)
                }
            }
            
            withContext(Dispatchers.Main) {
                binding.updateAllFiltersBtn.isEnabled = true
                binding.updateAllFiltersBtn.text = getString(R.string.update_all_filters)
                
                // Hide all progress indicators
                binding.adBlockerProgress.visibility = View.GONE
                binding.validLinksProgress.visibility = View.GONE
                binding.openLinksProgress.visibility = View.GONE
                
                // Show result toast
                val message = if (successCount == totalCount) {
                    "All filters updated successfully!"
                } else {
                    "$successCount of $totalCount filters updated successfully"
                }
                Toast.makeText(this@FilterUpdatesActivity, message, Toast.LENGTH_LONG).show()
                
                loadLastUpdatedTimes()
            }
        }
    }
    
    private fun clearAdBlockerTimeConstraints() {
        // Clear time constraints to force update
        val adBlockerPrefs = getSharedPreferences("AdBlockerPrefs", MODE_PRIVATE)
        adBlockerPrefs.edit().apply {
            putLong("lastUpdateTime", 0)
            putLong("lastFrequentUpdateTime", 0)
            apply()
        }
    }
    
    private fun saveUpdateTime(key: String) {
        val sharedPrefs = getSharedPreferences("FilterUpdateTimes", MODE_PRIVATE)
        sharedPrefs.edit().putLong(key, System.currentTimeMillis()).apply()
    }
}

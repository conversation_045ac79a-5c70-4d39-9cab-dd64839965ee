package com.elewashy.egyfilm.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.drawable.ColorDrawable
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.Settings
import android.util.Log
import android.view.Gravity
import android.view.WindowManager
import android.webkit.WebView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.activity.OnBackPressedCallback
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import com.google.android.material.button.MaterialButton
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.databinding.ActivityMainBinding
import com.elewashy.egyfilm.fragment.BrowseFragment
import com.elewashy.egyfilm.fragment.MoreOptionsBottomSheetFragment
import com.elewashy.egyfilm.service.DownloadService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import android.Manifest
import com.google.firebase.messaging.FirebaseMessaging
import com.elewashy.egyfilm.util.PermissionManager

class MainActivity : AppCompatActivity() {

    lateinit var binding: ActivityMainBinding
    private var backgroundTime: Long = 0
    private val THREE_HOURS_MS = 3 * 60 * 60 * 1000 // 3 hours in milliseconds
    private lateinit var browseFragment: BrowseFragment // Hold the single fragment instance
    private lateinit var permissionManager: PermissionManager

    companion object {
        private var isFullscreen: Boolean = false
        private var isManualFullscreen: Boolean = false
    }

    override fun onConfigurationChanged(newConfig: android.content.res.Configuration) {
        super.onConfigurationChanged(newConfig)
        when (newConfig.orientation) {
            android.content.res.Configuration.ORIENTATION_LANDSCAPE -> {
                if (!isManualFullscreen) {
                    changeFullscreen(true)
                    isFullscreen = true
                }
            }
            android.content.res.Configuration.ORIENTATION_PORTRAIT -> {
                if (!isManualFullscreen) {
                    changeFullscreen(false)
                    isFullscreen = false
                }
            }
        }
    }

    override fun onPause() {
        super.onPause()
        backgroundTime = System.currentTimeMillis()
    }

    override fun onResume() {
        super.onResume()

        // Check if 3 hours have passed in background
        val foregroundTime = System.currentTimeMillis()
        if (backgroundTime > 0 && (foregroundTime - backgroundTime) >= THREE_HOURS_MS) {
            // Clear history and navigate to home using lifecycleScope
            lifecycleScope.launch(Dispatchers.IO) {
                 // WebView operations must run on the UI thread
                withContext(Dispatchers.Main) {
                    try {
                        if (::browseFragment.isInitialized) { // Check if initialized
                            browseFragment.binding.webView.clearHistory()
                            browseFragment.binding.webView.loadUrl("https://egyfilm-app-multi.vercel.app/")
                        }
                    } catch (e: Exception) {
                    }
                }
            }
            backgroundTime = 0 // Reset timer
        }

        // Handle intent if MainActivity is resumed (e.g., from notification click)
        handleIntent(intent) // Intent from getIntent() can be null
    }

    override fun onNewIntent(intent: Intent) { // Correct signature: Intent is non-null
        super.onNewIntent(intent)
        // Handle intent if MainActivity is already running and receives a new intent
        handleIntent(intent) // Pass the non-null intent
        // Set the new intent to be the one returned by getIntent()
        setIntent(intent)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            window.attributes.layoutInDisplayCutoutMode =
                WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
        }

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // Initialize permission manager AFTER binding is ready
        permissionManager = PermissionManager(this)
        
        // Request all permissions after UI is set up
        requestAllPermissions()

        // Initialize the single BrowseFragment
        browseFragment = BrowseFragment(urlNew = "https://egyfilm-app-multi.vercel.app/")

        supportFragmentManager.beginTransaction()
            .replace(R.id.fragmentContainer, browseFragment) // Use the container ID from XML
            .commit()

        initializeView()
        initializeFirebaseMessaging()

        // Check initial orientation
        if (resources.configuration.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE) {
            changeFullscreen(true)
            isFullscreen = true
        }
        
        // Setup modern back press handling
        setupBackPressHandling()
    }

    private fun requestAllPermissions() {
        // Check if all permissions are already granted
        if (permissionManager.hasAllRequiredPermissions()) {
            // All permissions already granted, no need to show message
            Log.d("MainActivity", "All permissions already granted")
            return
        }
        
        // Use the new PermissionManager for better permission handling
        permissionManager.requestAllRequiredPermissions(
            onAllGranted = {
                Snackbar.make(
                    binding.root,
                    "All permissions granted successfully!",
                    Snackbar.LENGTH_SHORT
                ).show()
                Log.d("MainActivity", "All permissions granted")
            },
            onSomeOrAllDenied = {
                Snackbar.make(
                    binding.root,
                    "Some permissions were denied. App functionality may be limited.",
                    Snackbar.LENGTH_LONG
                ).show()
                Log.w("MainActivity", "Some permissions were denied")
            }
        )
    }


    
    private fun setupBackPressHandling() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // Use the single browseFragment instance, check if initialized first
                when {
                    ::browseFragment.isInitialized && browseFragment.binding.webView.canGoBack() -> {
                        browseFragment.binding.webView.goBack()
                    }
                    else -> {
                        // Disable this callback to allow default back behavior
                        isEnabled = false
                        onBackPressedDispatcher.onBackPressed()
                    }
                }
            }
        })
    }

    private fun initializeView() {

        binding.backButton.setOnClickListener {
            onBackPressedDispatcher.onBackPressed()
        }

        binding.goBtn.setOnClickListener {
            // Reload the main fragment with the home URL
             if (::browseFragment.isInitialized) {
                 browseFragment.binding.webView.loadUrl("https://egyfilm-app-multi.vercel.app/")
             }
        }

        binding.moreOptionsBtn.setOnClickListener {
            val bottomSheet = MoreOptionsBottomSheetFragment()
            bottomSheet.show(supportFragmentManager, MoreOptionsBottomSheetFragment.TAG)
        }
    }

    // --- Public methods for Bottom Sheet ---

    fun goBack() {
        // Use modern back press dispatcher instead of deprecated onBackPressed()
        onBackPressedDispatcher.onBackPressed()
    }

    fun goForward() {
        if (::browseFragment.isInitialized && browseFragment.binding.webView.canGoForward()) {
            browseFragment.binding.webView.goForward()
        }
    }


     fun toggleFullscreen() {
        isManualFullscreen = !isFullscreen // Track manual toggle
        isFullscreen = !isFullscreen // Toggle the state
        changeFullscreen(isFullscreen)
        // Optionally update button state if needed (though button is in bottom sheet now)
    }


    // --- End Public methods ---


    private fun changeFullscreen(enable: Boolean) {
        if (enable) {
            WindowCompat.setDecorFitsSystemWindows(window, false)
            WindowInsetsControllerCompat(window, binding.root).let { controller ->
                controller.hide(WindowInsetsCompat.Type.systemBars())
                controller.systemBarsBehavior =
                    WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
            }
        } else {
            WindowCompat.setDecorFitsSystemWindows(window, true)
            WindowInsetsControllerCompat(
                window,
                binding.root
            ).show(WindowInsetsCompat.Type.systemBars())
        }
    }

    private fun initializeFirebaseMessaging() {
        // Get FCM registration token
        FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
            if (!task.isSuccessful) {
                Log.w("MainActivity", "Fetching FCM registration token failed", task.exception)
                return@addOnCompleteListener
            }

            // Get new FCM registration token
            val token = task.result
            Log.d("MainActivity", "FCM Registration Token: $token")

            // Save token to SharedPreferences
            val sharedPref = getSharedPreferences("fcm_token", Context.MODE_PRIVATE)
            with(sharedPref.edit()) {
                putString("token", token)
                apply()
            }
        }

        // Subscribe to the 'all_users' topic
        FirebaseMessaging.getInstance().subscribeToTopic("all_users")
            .addOnCompleteListener { task ->
                var msg = "Successfully subscribed to notifications"
                if (!task.isSuccessful) {
                    msg = "Failed to subscribe to notifications"
                    Log.w("MainActivity", "Failed to subscribe to topic", task.exception)
                } else {
                    Log.d("MainActivity", "Subscribed to all_users topic")
                }
            }

    }

    private fun handleIntent(intent: Intent?) { // Accept nullable Intent
        // Perform null check before accessing action
        if (intent != null && intent.action == DownloadService.ACTION_OPEN_DOWNLOADS) {
            // Launch DownloadActivity directly
            val downloadIntent = Intent(this, DownloadActivity::class.java)
            // Add flags to bring existing task to front or start new one
            downloadIntent.flags = Intent.FLAG_ACTIVITY_REORDER_TO_FRONT or Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(downloadIntent)
        }
        
        // Handle notification actions - Any action opens the app
        if (intent != null && intent.hasExtra("action")) {
            val action = intent.getStringExtra("action")
            Log.d("MainActivity", "Notification action received: $action")
            
            // أي action يفتح التطبيق
            if (::browseFragment.isInitialized) {
                browseFragment.binding.webView.loadUrl("https://egyfilm-app-multi.vercel.app/")
            }
        }
    }
}

fun checkForInternet(context: Context): Boolean {
    val connectivityManager =
        context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager

    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
        val network = connectivityManager.activeNetwork ?: return false
        val activeNetwork = connectivityManager.getNetworkCapabilities(network) ?: return false
        activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                activeNetwork.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
    } else {
        // For older versions, use modern ConnectivityManager approach when possible
        try {
            val network = connectivityManager.activeNetwork
            if (network != null) {
                val capabilities = connectivityManager.getNetworkCapabilities(network)
                capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
            } else {
                // Ultimate fallback for very old devices
                val activeNetworkInfo = connectivityManager.activeNetworkInfo
                activeNetworkInfo?.isConnected == true
            }
        } catch (e: SecurityException) {
            // If permission is not granted, assume no connection
            false
        }
    }
}

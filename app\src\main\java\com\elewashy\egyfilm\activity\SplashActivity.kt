package com.elewashy.egyfilm.activity

import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.View
import android.view.animation.DecelerateInterpolator
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.elewashy.egyfilm.BuildConfig
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.activity.DownloadActivity
import com.elewashy.egyfilm.databinding.ActivitySplashBinding
import com.elewashy.egyfilm.fragment.AdBlocker
import com.elewashy.egyfilm.fragment.OpenLinkValidator
import com.elewashy.egyfilm.fragment.ValidLinkChecker
import com.elewashy.egyfilm.model.UpdateResponse
import com.elewashy.egyfilm.service.DownloadService
import com.google.gson.Gson
import java.io.IOException
import java.net.HttpURLConnection
import java.net.URL
import kotlin.concurrent.thread

class SplashActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySplashBinding
    private val handler = Handler(Looper.getMainLooper())
    private var updateResponse: UpdateResponse? = null
    
    // Modern activity result launcher
    private lateinit var installPermissionLauncher: ActivityResultLauncher<Intent>
    
    companion object {
        private const val UPDATE_CHECK_URL = "https://ad-hosts.vercel.app/update.json"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySplashBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize AdBlocker in background to avoid blocking startup
        thread {
            val adBlocker = AdBlocker.getInstance(this@SplashActivity)
            adBlocker.updateEasyList()
        }

        // Initialize ValidLinkChecker and start update in background
        val validLinkChecker = ValidLinkChecker.getInstance(this)
        thread {
            validLinkChecker.updateValidLinks()
        }

        // Initialize OpenLinkValidator and start update in background
        val openLinkValidator = OpenLinkValidator.getInstance(this)
        thread {
            openLinkValidator.updateOpenLinks()
        }

        // Initialize modern activity result launcher
        setupActivityResultLaunchers()
        
        setupInitialAnimations()
        setupRetryButton()
        setupUpdateDialog()
        checkForUpdates()
    }
    
    private fun setupActivityResultLaunchers() {
        // Modern replacement for startActivityForResult
        installPermissionLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (canInstallApks()) {
                updateResponse?.let { response ->
                    startDownload(response.apk_url)
                }
            } else {
                Toast.makeText(this, "You must allow installation of apps from unknown sources", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun setupInitialAnimations() {
        // Initially hide views
        binding.splashLogo.apply {
            alpha = 0f
            scaleX = 0.7f
            scaleY = 0.7f
            translationY = -50f
        }
        
        binding.loadingProgress.apply {
            alpha = 0f
            scaleX = 0.5f
            scaleY = 0.5f
            translationY = 30f
        }

        // Animate logo first with enhanced animation
        binding.splashLogo.animate()
            .alpha(1f)
            .scaleX(1f)
            .scaleY(1f)
            .translationY(0f)
            .setDuration(900)
            .setInterpolator(DecelerateInterpolator(2.0f))
            .setStartDelay(200)
            .withEndAction {
                // Then animate loading indicator with spring effect
                binding.loadingProgress.animate()
                    .alpha(1f)
                    .scaleX(1f)
                    .scaleY(1f)
                    .translationY(0f)
                    .setDuration(700)
                    .setInterpolator(DecelerateInterpolator(1.5f))
                    .setStartDelay(100)
                    .start()
            }
            .start()
    }

    private fun setupRetryButton() {
        binding.retryButton.setOnClickListener {
            // Animate out error UI with enhanced effects
            binding.noInternetText.animate()
                .alpha(0f)
                .scaleX(0.8f)
                .scaleY(0.8f)
                .translationY(-20f)
                .setDuration(350)
                .setInterpolator(DecelerateInterpolator(1.5f))
                .withEndAction {
                    binding.noInternetText.visibility = View.GONE
                    binding.noInternetText.translationY = 0f
                }
                .start()

            binding.retryButton.animate()
                .alpha(0f)
                .scaleX(0.8f)
                .scaleY(0.8f)
                .translationY(20f)
                .setDuration(350)
                .setInterpolator(DecelerateInterpolator(1.5f))
                .withEndAction {
                    binding.retryButton.visibility = View.GONE
                    binding.retryButton.translationY = 0f

                    // Show loading progress with enhanced animation
                    binding.loadingProgress.apply {
                        visibility = View.VISIBLE
                        alpha = 0f
                        scaleX = 0.5f
                        scaleY = 0.5f
                        translationY = 30f
                    }
                    
                    binding.loadingProgress.animate()
                        .alpha(1f)
                        .scaleX(1f)
                        .scaleY(1f)
                        .translationY(0f)
                        .setDuration(600)
                        .setInterpolator(DecelerateInterpolator(1.8f))
                        .setStartDelay(100)
                        .withEndAction {
                            checkInternetAndProceed()
                        }
                        .start()
                }
                .start()
        }
    }

    private fun checkInternetAndProceed() {
        handler.postDelayed({
            if (isInternetAvailable()) {
                proceedToMainActivity()
            } else {
                showNoInternetUI()
            }
        }, 2500) // Adjusted for new animation duration
    }
    
    private fun checkForUpdates() {
        if (!isInternetAvailable()) {
            checkInternetAndProceed()
            return
        }
        
        thread {
            try {
                val url = URL(UPDATE_CHECK_URL)
                val connection = url.openConnection() as HttpURLConnection
                connection.requestMethod = "GET"
                connection.connectTimeout = 10000
                connection.readTimeout = 10000
                
                val responseCode = connection.responseCode
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val response = connection.inputStream.bufferedReader().use { it.readText() }
                    val gson = Gson()
                    val updateInfo = gson.fromJson<UpdateResponse>(response, UpdateResponse::class.java)
                    
                    runOnUiThread {
                        if (isUpdateAvailable(updateInfo.latest_version)) {
                            updateResponse = updateInfo
                            showUpdateDialog(updateInfo)
                        } else {
                            checkInternetAndProceed()
                        }
                    }
                } else {
                    runOnUiThread {
                        checkInternetAndProceed()
                    }
                }
                connection.disconnect()
            } catch (e: IOException) {
                runOnUiThread {
                    checkInternetAndProceed()
                }
            }
        }
    }
    
    private fun isUpdateAvailable(latestVersion: String): Boolean {
        val currentVersion = BuildConfig.VERSION_NAME
        return compareVersions(latestVersion, currentVersion) > 0
    }
    
    private fun compareVersions(version1: String, version2: String): Int {
        val parts1 = version1.split(".").map { it.toIntOrNull() ?: 0 }
        val parts2 = version2.split(".").map { it.toIntOrNull() ?: 0 }
        
        val maxLength = maxOf(parts1.size, parts2.size)
        
        for (i in 0 until maxLength) {
            val v1 = if (i < parts1.size) parts1[i] else 0
            val v2 = if (i < parts2.size) parts2[i] else 0
            
            when {
                v1 > v2 -> return 1
                v1 < v2 -> return -1
            }
        }
        return 0
    }

    private fun showNoInternetUI() {
        runOnUiThread {
            try {
                // Prepare loading to scale down while fading
                binding.loadingProgress.animate()
                    .alpha(0f)
                    .scaleX(0.8f)
                    .scaleY(0.8f)
                    .setDuration(400)
                    .setInterpolator(DecelerateInterpolator(1.2f))
                    .withEndAction {
                        binding.loadingProgress.visibility = View.GONE

                        // Prepare error UI
                        binding.noInternetText.apply {
                            visibility = View.VISIBLE
                            alpha = 0f
                            scaleX = 0.9f
                            scaleY = 0.9f
                        }
                        binding.retryButton.apply {
                            visibility = View.VISIBLE
                            alpha = 0f
                            scaleX = 0.9f
                            scaleY = 0.9f
                        }

                        // Animate error UI
                        binding.noInternetText.animate()
                            .alpha(1f)
                            .scaleX(1f)
                            .scaleY(1f)
                            .setDuration(500)
                            .setInterpolator(DecelerateInterpolator())
                            .start()

                        binding.retryButton.animate()
                            .alpha(1f)
                            .scaleX(1f)
                            .scaleY(1f)
                            .setStartDelay(100)
                            .setDuration(500)
                            .setInterpolator(DecelerateInterpolator())
                            .start()
                    }
                    .start()

            } catch (e: Exception) {
                // Fallback in case of animation failure
                binding.loadingProgress.visibility = View.GONE
                binding.noInternetText.visibility = View.VISIBLE
                binding.noInternetText.alpha = 1f
                binding.retryButton.visibility = View.VISIBLE
                binding.retryButton.alpha = 1f
            }
        }
    }

    private fun proceedToMainActivity() {
        // Animate out splash screen elements with enhanced effects
        binding.splashLogo.animate()
            .alpha(0f)
            .scaleX(1.2f)
            .scaleY(1.2f)
            .translationY(-100f)
            .setDuration(500)
            .setInterpolator(DecelerateInterpolator(1.8f))
            .start()

        binding.loadingProgress.animate()
            .alpha(0f)
            .scaleX(0.5f)
            .scaleY(0.5f)
            .translationY(50f)
            .setDuration(450)
            .setInterpolator(DecelerateInterpolator(1.5f))
            .setStartDelay(50)
            .withEndAction {
                val intent = Intent(this, MainActivity::class.java)
                startActivity(intent)
                // Modern transition using overrideActivityTransition (API 34+) or overridePendingTransition for older versions
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                    overrideActivityTransition(OVERRIDE_TRANSITION_OPEN, R.anim.activity_slide_in_up, R.anim.activity_fade_out_down)
                } else {
                    @Suppress("DEPRECATION")
                    overridePendingTransition(R.anim.activity_slide_in_up, R.anim.activity_fade_out_down)
                }
                finish()
            }
            .start()
    }

    private fun isInternetAvailable(): Boolean {
        val connectivityManager = getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val network = connectivityManager.activeNetwork
            val capabilities = connectivityManager.getNetworkCapabilities(network)
            capabilities?.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) == true
        } else {
            @Suppress("DEPRECATION")
            val networkInfo = connectivityManager.activeNetworkInfo
            @Suppress("DEPRECATION")
            networkInfo != null && networkInfo.isConnected
        }
    }
    
    private fun setupUpdateDialog() {
        binding.updateButton.setOnClickListener {
            updateResponse?.let { response ->
                if (canInstallApps()) {
                    startDownload(response.apk_url)
                } else {
                    requestInstallPermission()
                }
            }
        }
    }
    
    private fun showUpdateDialog(updateInfo: UpdateResponse) {
        binding.updateMessage.text = updateInfo.update_message
        binding.updateDialog.apply {
            visibility = View.VISIBLE
            alpha = 0f
            scaleX = 0.8f
            scaleY = 0.8f
        }
        
        binding.updateDialog.animate()
            .alpha(1f)
            .scaleX(1f)
            .scaleY(1f)
            .setDuration(400)
            .setInterpolator(DecelerateInterpolator())
            .start()
    }
    
    private fun hideUpdateDialog() {
        binding.updateDialog.animate()
            .alpha(0f)
            .scaleX(0.8f)
            .scaleY(0.8f)
            .setDuration(300)
            .setInterpolator(DecelerateInterpolator())
            .withEndAction {
                binding.updateDialog.visibility = View.GONE
            }
            .start()
    }
    
    private fun canInstallApps(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            packageManager.canRequestPackageInstalls()
        } else {
            true
        }
    }
    
    private fun requestInstallPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
                .setData(Uri.parse("package:$packageName"))
            installPermissionLauncher.launch(intent)
        }
    }
    
    @Deprecated("Use registerForActivityResult instead")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        // This method is deprecated and replaced with modern activity result launchers
        // All functionality has been moved to setupActivityResultLaunchers()
    }
    
    private fun canInstallApks(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            packageManager.canRequestPackageInstalls()
        } else {
            true
        }
    }
    
    private fun startDownload(url: String) {
        binding.updateButton.isEnabled = false
        binding.downloadStatus.text = "Starting download..."
        
        // Create and start download through DownloadService
        val intent = DownloadService.createStartIntent(
            context = this,
            url = url,
            fileName = "egyfilm_update.apk",
            mimeType = "application/vnd.android.package-archive",
            userAgent = null,
            referer = null, 
            origin = null,
            cookies = null,
            source = "UPDATE"
        )
        startService(intent)

        // Launch DownloadActivity
        val downloadActivityIntent = Intent(this, DownloadActivity::class.java)
        startActivity(downloadActivityIntent)
        
        // Hide update dialog since download is starting
        hideUpdateDialog()
    }
}

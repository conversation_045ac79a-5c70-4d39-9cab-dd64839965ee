package com.elewashy.egyfilm.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.databinding.ItemDownloadBinding
import com.elewashy.egyfilm.model.DownloadItem
import com.elewashy.egyfilm.model.DownloadStatus

class DownloadAdapter(
    private val context: Context,
    private val onPauseClick: (DownloadItem) -> Unit,
    private val onResumeClick: (DownloadItem) -> Unit,
    private val onCancelClick: (DownloadItem) -> Unit,
    private val onRetryClick: (DownloadItem) -> Unit, // Added for failed downloads
    private val onItemClick: (DownloadItem) -> Unit, // Added for clicking the item itself
    private val onOpenClick: (DownloadItem) -> Unit, // Added for opening completed files
    private val onDeleteClick: (DownloadItem) -> Unit // Added for deleting completed files
) : ListAdapter<DownloadItem, DownloadAdapter.DownloadViewHolder>(DownloadDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DownloadViewHolder {
        val binding = ItemDownloadBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return DownloadViewHolder(binding)
    }

    override fun onBindViewHolder(holder: DownloadViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class DownloadViewHolder(private val binding: ItemDownloadBinding) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: DownloadItem) {
            binding.tvFileName.text = item.fileName
            binding.pbDownloadProgress.progress = item.progress
            binding.pbDownloadProgress.isIndeterminate = item.status == DownloadStatus.PENDING || (item.status == DownloadStatus.DOWNLOADING && item.totalBytes <= 0)

            // Format size and status text
            val totalMb = if (item.totalBytes > 0) String.format("%.1f MB", item.totalBytes / (1024.0 * 1024.0)) else "Unknown size"
            val downloadedMb = String.format("%.1f MB", item.downloadedBytes / (1024.0 * 1024.0))

            binding.tvDownloadStatus.text = when (item.status) {
                DownloadStatus.PENDING -> "Pending..."
                DownloadStatus.DOWNLOADING -> "Downloading: ${item.progress}% ($downloadedMb / $totalMb)"
                DownloadStatus.PAUSED -> "Paused: ${item.progress}% ($downloadedMb / $totalMb)"
                DownloadStatus.COMPLETED -> "Completed: $downloadedMb"
                DownloadStatus.FAILED -> {
                    if (item.downloadedBytes > 0) {
                        "Paused (${item.progress}%) - Will auto-retry"
                    } else {
                        "Failed - Will auto-retry"
                    }
                }
                DownloadStatus.CANCELLED -> "Cancelled"
            }

            // Hide speed for now, can be added later if service provides it
            binding.tvDownloadSpeed.visibility = View.GONE

            // Configure buttons based on status
            when (item.status) {
                DownloadStatus.DOWNLOADING, DownloadStatus.PENDING -> {
                    binding.btnPauseResume.visibility = View.VISIBLE
                    binding.btnPauseResume.setImageResource(R.drawable.ic_pause)
                    binding.btnPauseResume.contentDescription = context.getString(R.string.pause_resume_button_desc) // Pause action
                    binding.btnPauseResume.setOnClickListener { onPauseClick(item) }
                    binding.btnCancel.visibility = View.VISIBLE
                    binding.btnCancel.setImageResource(R.drawable.ic_cancel)
                    binding.btnCancel.contentDescription = context.getString(R.string.cancel_button_desc)
                    binding.btnCancel.setOnClickListener { onCancelClick(item) }
                }
                DownloadStatus.PAUSED -> {
                    binding.btnPauseResume.visibility = View.VISIBLE
                    binding.btnPauseResume.setImageResource(R.drawable.ic_play_arrow) // Use a play icon
                    binding.btnPauseResume.contentDescription = context.getString(R.string.pause_resume_button_desc) // Resume action
                    binding.btnPauseResume.setOnClickListener { onResumeClick(item) }
                    binding.btnCancel.visibility = View.VISIBLE
                    binding.btnCancel.setImageResource(R.drawable.ic_cancel)
                    binding.btnCancel.contentDescription = context.getString(R.string.cancel_button_desc)
                    binding.btnCancel.setOnClickListener { onCancelClick(item) }
                }
                 DownloadStatus.FAILED -> {
                    binding.btnPauseResume.visibility = View.VISIBLE
                    binding.btnPauseResume.setImageResource(R.drawable.ic_refresh) // Use a retry icon
                    binding.btnPauseResume.contentDescription = context.getString(R.string.retry_button_desc)
                    binding.btnPauseResume.setOnClickListener { onRetryClick(item) } // Use retry action
                    binding.btnCancel.visibility = View.VISIBLE // Allow cancelling failed downloads
                    binding.btnCancel.setImageResource(R.drawable.ic_cancel)
                    binding.btnCancel.contentDescription = context.getString(R.string.cancel_button_desc)
                    binding.btnCancel.setOnClickListener { onCancelClick(item) }
                }
                DownloadStatus.COMPLETED -> {
                    // Show open and delete buttons for completed items
                    binding.btnPauseResume.visibility = View.VISIBLE
                    binding.btnPauseResume.setImageResource(R.drawable.ic_open_file)
                    binding.btnPauseResume.contentDescription = context.getString(R.string.open_file_button_desc)
                    binding.btnPauseResume.setOnClickListener { onOpenClick(item) }
                    binding.btnCancel.visibility = View.VISIBLE
                    binding.btnCancel.setImageResource(R.drawable.ic_delete)
                    binding.btnCancel.contentDescription = context.getString(R.string.delete_button_desc)
                    binding.btnCancel.setOnClickListener { onDeleteClick(item) }
                }
                DownloadStatus.CANCELLED -> {
                    // Hide buttons for cancelled items
                    binding.btnPauseResume.visibility = View.VISIBLE
                    binding.btnPauseResume.setImageResource(R.drawable.ic_refresh) // Use a retry icon
                    binding.btnPauseResume.contentDescription = context.getString(R.string.retry_button_desc)
                    binding.btnPauseResume.setOnClickListener { onRetryClick(item) } // Use retry action
                    binding.btnCancel.visibility = View.VISIBLE
                    binding.btnCancel.setImageResource(R.drawable.ic_delete)
                    binding.btnCancel.contentDescription = context.getString(R.string.delete_button_desc)
                    binding.btnCancel.setOnClickListener { onDeleteClick(item) }
                }
            }

            // Set file icon based on download status
            when (item.status) {
                DownloadStatus.COMPLETED -> {
                    binding.ivFileIcon.setImageResource(R.drawable.ic_file_complete)
                }
                else -> {
                    binding.ivFileIcon.setImageResource(R.drawable.ic_download_file)
                }
            }

            // Set click listener for the entire item view
            binding.root.setOnClickListener {
                // Trigger the item click action for completed files
                if (item.status == DownloadStatus.COMPLETED) {
                    onItemClick(item)
                }
            }
        }
    }

    // DiffUtil Callback for efficient list updates
    class DownloadDiffCallback : DiffUtil.ItemCallback<DownloadItem>() {
        override fun areItemsTheSame(oldItem: DownloadItem, newItem: DownloadItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: DownloadItem, newItem: DownloadItem): Boolean {
            // Compare relevant fields that affect UI display
            return oldItem.status == newItem.status &&
                   oldItem.progress == newItem.progress &&
                   oldItem.downloadedBytes == newItem.downloadedBytes &&
                   oldItem.totalBytes == newItem.totalBytes
        }
    }
}

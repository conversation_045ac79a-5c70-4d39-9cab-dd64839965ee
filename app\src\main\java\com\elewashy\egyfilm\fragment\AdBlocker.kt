package com.elewashy.egyfilm.fragment

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.net.Uri
import android.os.Handler
import android.os.Looper
import android.os.Build
import androidx.core.app.NotificationCompat
import com.elewashy.egyfilm.R
import java.net.URL
import java.util.concurrent.*
import java.util.concurrent.Callable
import java.util.concurrent.Executors
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

class AdBlocker private constructor(private val context: Context) { // Make constructor private
    private val adHosts = mutableSetOf<String>()
    private val sharedPreferences = context.getSharedPreferences("AdBlockerPrefs", Context.MODE_PRIVATE)
    private data class ListMetadata(val etag: String?, val lastModified: String?)
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    private val CHANNEL_ID = "adblock_channel"
    // Notification IDs for different types
    private val NOTIFICATION_FREQUENT_UPDATE = 1
    private val NOTIFICATION_EASYLIST_UPDATE = 2
    private val HOURS_MS = 6 * 60 * 60 * 1000 // 6 hours in milliseconds
    private val FREQUENT_HOURS_MS = 1 * 60 * 60 * 1000 // 1 hours in milliseconds for frequent updates
    private val FREQUENT_UPDATE_URL = "https://ad-hosts.vercel.app/filter.txt"

    init {
        createNotificationChannel()
        loadLocalHosts()
        // Start background update without blocking initialization
        startBackgroundUpdate()
    }

    companion object {
        @Volatile private var instance: AdBlocker? = null
        private val lock = ReentrantLock()

        fun getInstance(context: Context): AdBlocker {
            return instance ?: lock.withLock {
                instance ?: AdBlocker(context.applicationContext).also { instance = it }
            }
        }
    }

    private fun startBackgroundUpdate() {
        // Run updates in background thread without blocking initialization
        Thread {
            updateFrequentList()
        }.start()
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "AdBlock Updates"
            val descriptionText = "Notifications for AdBlock list updates"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun getStoredMetadata(url: String): ListMetadata {
        return ListMetadata(
            sharedPreferences.getString("${url}_etag", null),
            sharedPreferences.getString("${url}_lastModified", null)
        )
    }

    private fun saveMetadata(url: String, metadata: ListMetadata) {
        sharedPreferences.edit().apply {
            putString("${url}_etag", metadata.etag)
            putString("${url}_lastModified", metadata.lastModified)
            apply()
        }
    }

    private fun checkIfListModified(url: String): Triple<Boolean, String, Set<String>> {
        try {
            // First do a HEAD request to check headers
            val connection = URL(url).openConnection() as java.net.HttpURLConnection
            connection.requestMethod = "HEAD"
            connection.setRequestProperty("User-Agent", "Mozilla/5.0")
            connection.connectTimeout = 10000 // 10 seconds
            connection.readTimeout = 15000 // 15 seconds
            
            val metadata = getStoredMetadata(url)
            if (metadata.etag != null) {
                connection.setRequestProperty("If-None-Match", metadata.etag)
            }
            if (metadata.lastModified != null) {
                connection.setRequestProperty("If-Modified-Since", metadata.lastModified)
            }
            
            connection.connect()
            
            // Get existing hosts for this URL
            val oldHosts = sharedPreferences.getStringSet("${url}_hosts", null)
            
            // If we have no stored hosts or server indicates content changed
            if (oldHosts == null || connection.responseCode == 200) {
                // Disconnect HEAD request
                connection.disconnect()
                
                // Make a GET request to get content
                val getConnection = URL(url).openConnection() as java.net.HttpURLConnection
                getConnection.setRequestProperty("User-Agent", "Mozilla/5.0")
                getConnection.connectTimeout = 10000 // 10 seconds
                getConnection.readTimeout = 30000 // 30 seconds for reading content
                val content = getConnection.inputStream.bufferedReader().use { it.readText() }
                val newHosts = parseEasyList(content)
                
                // Save new metadata and hosts
                val newEtag = getConnection.getHeaderField("ETag")
                val newLastModified = getConnection.getHeaderField("Last-Modified")
                saveMetadata(url, ListMetadata(newEtag, newLastModified))
                sharedPreferences.edit().putStringSet("${url}_hosts", newHosts).apply()
                
                return Triple(true, content, newHosts)
            }
            
            // No changes, return existing hosts
            return Triple(false, "", oldHosts ?: emptySet())
        } catch (e: Exception) {
            e.printStackTrace()
            return Triple(false, "", emptySet())
        }
    }

    fun updateFrequentList() {
        // Check if 6 hours have passed since last frequent update
        val lastFrequentUpdateTime = sharedPreferences.getLong("lastFrequentUpdateTime", 0)
        val currentTime = System.currentTimeMillis()
        
        if (currentTime - lastFrequentUpdateTime < FREQUENT_HOURS_MS) {
            return // Skip update if 24 hours haven't passed
        }
        
        val executor = Executors.newSingleThreadExecutor()
        
        // Show initial notification
        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_refresh)
            .setContentTitle("Checking Frequent AdBlock List")
            .setContentText("Checking for changes...")
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setProgress(100, 0, true)
            .setOngoing(true)
        
        notificationManager.notify(NOTIFICATION_FREQUENT_UPDATE, builder.build())

        val future = executor.submit {
            try {
                val (modified, _, hosts) = checkIfListModified(FREQUENT_UPDATE_URL)
                
                if (hosts.isNotEmpty()) {
                    // Get existing hosts from all other lists
                    val existingHosts = sharedPreferences.getStringSet("adHosts", emptySet()) ?: emptySet()
                    val otherHosts = existingHosts.filterNot { host -> 
                        val oldFrequentHosts = sharedPreferences.getStringSet("${FREQUENT_UPDATE_URL}_hosts", emptySet()) ?: emptySet()
                        oldFrequentHosts.contains(host)
                    }.toSet()
                    
                    // Merge new frequent hosts with existing other hosts
                    val mergedHosts = otherHosts + hosts
                    
                    // Update the active set and save
                    synchronized(adHosts) {
                        adHosts.clear()
                        adHosts.addAll(mergedHosts)
                    }
                    saveLocalHosts(adHosts)
                    
                    // Update last frequent update time
                    sharedPreferences.edit().putLong("lastFrequentUpdateTime", currentTime).apply()
                    
                    // Show success notification
                    builder.setContentText(if (modified) "Updated frequent list" else "Frequent list is up to date")
                        .setProgress(0, 0, false)
                        .setOngoing(false)
                } else {
                    builder.setContentText("Error updating frequent list")
                        .setProgress(0, 0, false)
                        .setOngoing(false)
                }
                
                notificationManager.notify(NOTIFICATION_FREQUENT_UPDATE, builder.build())
                
                // Auto dismiss after 3 seconds
                Handler(Looper.getMainLooper()).postDelayed({
                    notificationManager.cancel(NOTIFICATION_FREQUENT_UPDATE)
                }, 3000)
                
            } catch (e: Exception) {
                e.printStackTrace()
                builder.setContentText("Error updating frequent list")
                    .setProgress(0, 0, false)
                    .setOngoing(false)
                notificationManager.notify(NOTIFICATION_FREQUENT_UPDATE, builder.build())
            }
        }

        // Set a timeout for the frequent update task
        try {
            future.get(45, TimeUnit.SECONDS) // 45 second timeout
        } catch (e: Exception) {
            e.printStackTrace()
            // Show timeout notification
            builder.setContentText("Frequent update timed out")
                .setProgress(0, 0, false)
                .setOngoing(false)
            notificationManager.notify(NOTIFICATION_FREQUENT_UPDATE, builder.build())
            
            // Auto dismiss after 3 seconds
            Handler(Looper.getMainLooper()).postDelayed({
                notificationManager.cancel(NOTIFICATION_FREQUENT_UPDATE)
            }, 3000)
        }

        // Don't block - just shutdown the executor
        executor.shutdown()
    }

    fun updateEasyList() {
        // Check if 72 hours have passed since last update
        val lastUpdateTime = sharedPreferences.getLong("lastUpdateTime", 0)
        val currentTime = System.currentTimeMillis()
        
        if (currentTime - lastUpdateTime < HOURS_MS) {
            return // Skip update if 72 hours haven't passed
        }
        // Show initial notification
        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_refresh)
            .setContentTitle("Checking AdBlock Lists")
            .setContentText("Checking for changes...")
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setProgress(100, 0, true)
            .setOngoing(true)
        
        notificationManager.notify(NOTIFICATION_EASYLIST_UPDATE, builder.build())

        val executor = Executors.newFixedThreadPool(1)
        val completedDownloads = AtomicInteger(0)
        val easyListUrls = listOf(
//            "https://raw.githubusercontent.com/AdguardTeam/FiltersRegistry/master/filters/filter_2_Base/filter.txt",
//            "https://raw.githubusercontent.com/AdguardTeam/FiltersRegistry/master/filters/filter_15_DnsFilter/filter.txt",
//             "https://o0.pages.dev/Lite/adblock.txt",
//            "https://adguardteam.github.io/AdGuardSDNSFilter/Filters/filter.txt",
//            "https://ublockorigin.github.io/uAssetsCDN/filters/badware.min.txt",
//             "https://filters.adtidy.org/extension/ublock/filters/2_without_easylist.txt",
//             "https://filters.adtidy.org/extension/ublock/filters/11.txt",
            "https://easylist.to/easylist/easylist.txt",
//             "https://ublockorigin.pages.dev/filters/filters.min.txt",
//            "https://raw.githubusercontent.com/AdguardTeam/FiltersRegistry/master/filters/filter_11_Mobile/filter.txt",
//            "https://raw.githubusercontent.com/AdguardTeam/FiltersRegistry/master/filters/filter_19_Annoyances_Popups/filter.txt"
        )

        val totalLists = easyListUrls.size
        
        val futures = easyListUrls.map { url ->
            executor.submit(Callable {
                try {
                    val (modified, content, hosts) = checkIfListModified(url)
                    val currentProgress = completedDownloads.incrementAndGet()
                    
                    // Update progress notification
                    val progress = (currentProgress * 100) / totalLists
                    if (modified) {
                        builder.setContentText("Downloading updated list ($completedDownloads/$totalLists)")
                    } else {
                        builder.setContentText("Checking lists ($completedDownloads/$totalLists)")
                    }
                        .setProgress(100, progress, false)
                    notificationManager.notify(NOTIFICATION_EASYLIST_UPDATE, builder.build())
                    
                    Triple(modified, content, hosts)
                } catch (e: Exception) {
                    e.printStackTrace()
                    completedDownloads.incrementAndGet()
                    Triple(false, "", emptySet())
                }
            })
        }

        executor.shutdown()
        try {
            // Wait up to 3 minutes for all tasks to complete
            if (!executor.awaitTermination(3, TimeUnit.MINUTES)) {
                executor.shutdownNow()
            }
        } catch (e: InterruptedException) {
            executor.shutdownNow()
        }

        try {
            // Collect successful results
            val results = futures.map { it.get() }
            val modifiedResults = results.filter { it.first }
            
            // Get hosts from both modified and unmodified lists
            val allHosts = results
                .filter { (_, content, hosts) -> hosts.isNotEmpty() }
                .flatMap { (_, _, hosts) -> hosts }
                .toSet()
            
            // Update if we have any hosts (either from new downloads or stored)
            if (allHosts.isNotEmpty()) {
                // Get the frequent list hosts
                val frequentHosts = sharedPreferences.getStringSet("${FREQUENT_UPDATE_URL}_hosts", emptySet()) ?: emptySet()
                
                // Merge all hosts together
                val mergedHosts = allHosts + frequentHosts
                
                // Update the active set and save
                synchronized(adHosts) {
                    adHosts.clear()
                    adHosts.addAll(mergedHosts)
                }
                saveLocalHosts(adHosts)
                
                // Update last update time
                sharedPreferences.edit().putLong("lastUpdateTime", currentTime).apply()
                
                // Show success notification with count of updated lists
                val updatedCount = modifiedResults.size
                builder.setContentText("Updated $updatedCount ${if (updatedCount == 1) "list" else "lists"}")
                    .setProgress(0, 0, false)
                    .setOngoing(false)
                notificationManager.notify(NOTIFICATION_EASYLIST_UPDATE, builder.build())
                
                // Auto dismiss after 3 seconds
                Handler(Looper.getMainLooper()).postDelayed({
                    notificationManager.cancel(NOTIFICATION_EASYLIST_UPDATE)
                }, 3000)
            } else {
                // Show notification about lists being up to date
                builder.setContentText("All ${totalLists} lists are up to date")
                    .setProgress(0, 0, false)
                    .setOngoing(false)
                notificationManager.notify(NOTIFICATION_EASYLIST_UPDATE, builder.build())
                
                // Auto dismiss after 3 seconds
                Handler(Looper.getMainLooper()).postDelayed({
                    notificationManager.cancel(NOTIFICATION_EASYLIST_UPDATE)
                }, 3000)
            }
        } catch (e: Exception) {
            // Show error notification
            builder.setContentText("Error updating AdBlock lists")
                .setProgress(0, 0, false)
                .setOngoing(false)
            notificationManager.notify(NOTIFICATION_EASYLIST_UPDATE, builder.build())
            
            // Auto dismiss after 3 seconds
            Handler(Looper.getMainLooper()).postDelayed({
                notificationManager.cancel(NOTIFICATION_EASYLIST_UPDATE)
            }, 3000)
            e.printStackTrace()
        }
    }

    private fun parseEasyList(easyList: String): Set<String> {
        val hosts = mutableSetOf<String>()
        easyList.lines().forEach { line ->
            if (line.contains(",")) {
                return@forEach
            }

            if (!line.startsWith("!") && !line.startsWith("@@||") && line.contains("||")) {
                val domain = line.split("||")[1].split("^")[0]
                hosts.add(domain)
            }
        }
        return hosts
    }

    private fun saveLocalHosts(hosts: Set<String>) {
        sharedPreferences.edit().putStringSet("adHosts", hosts).apply()
    }

    private fun loadLocalHosts() {
        // Load hosts in background to avoid blocking initialization
        Thread {
            val savedHosts = sharedPreferences.getStringSet("adHosts", emptySet()) ?: emptySet()
            synchronized(adHosts) {
                adHosts.addAll(savedHosts)
            }
        }.start()
    }

    fun isAd(url: String): Boolean {
        val uri = Uri.parse(url)
        val host = uri.host ?: return false
        val path = uri.path ?: ""
        val query = uri.query ?: ""

        return synchronized(adHosts) {
            adHosts.any { host.contains(it) || path.contains(it) || query.contains(it) }
        }
    }
}

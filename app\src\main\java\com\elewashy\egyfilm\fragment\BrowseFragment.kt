package com.elewashy.egyfilm.fragment

import android.annotation.SuppressLint
import android.content.Intent
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.content.ContentValues
import android.text.SpannableStringBuilder
import android.util.Base64
import android.view.*
import android.webkit.*
import androidx.core.app.ShareCompat
import androidx.fragment.app.Fragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.imageview.ShapeableImageView
import com.google.android.material.snackbar.Snackbar
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.activity.MainActivity
import com.elewashy.egyfilm.fragment.AdBlocker
import com.elewashy.egyfilm.fragment.ValidLinkChecker
import com.elewashy.egyfilm.fragment.OpenLinkValidator
import com.elewashy.egyfilm.databinding.FragmentBrowseBinding
import java.io.ByteArrayOutputStream
import java.io.ByteArrayInputStream
import java.util.concurrent.Executors
import java.net.URL
import java.util.Timer
import kotlin.concurrent.scheduleAtFixedRate
import androidx.appcompat.app.AlertDialog
import java.util.concurrent.*
import android.content.Context
import java.util.concurrent.Callable
import kotlin.text.Regex
import android.os.Build
import android.os.Environment
import android.webkit.URLUtil
import java.io.OutputStream
import com.elewashy.egyfilm.service.DownloadService
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.Toast
import java.net.URLDecoder
import android.content.pm.ActivityInfo
import android.app.Activity
import java.io.File
import com.elewashy.egyfilm.fragment.RegexPatterns
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout

class BrowseFragment(private var urlNew: String = "https://egyfilm-app-multi.vercel.app/") : Fragment() {

    lateinit var binding: FragmentBrowseBinding
    var webIcon: Bitmap? = null


    private var wasInFullscreen = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_browse, container, false)
        binding = FragmentBrowseBinding.bind(view)
        registerForContextMenu(binding.webView)

        binding.swipeRefreshLayout.apply {
            setOnRefreshListener {
                val mainActivity = requireActivity() as MainActivity
                val motionProgress = mainActivity.binding.root.progress

                if (motionProgress < 0.5f) {
                    // Header is visible - refresh without clearing cache
                    binding.webView.reload()
                } else {
                    // Header is hidden - show it
                    mainActivity.binding.root.transitionToStart()
                    isRefreshing = false
                }
            }

            setColorSchemeResources(
                R.color.egyfilm_red,
                R.color.egyfilm_red_dark
            )
            setProgressBackgroundColorSchemeResource(R.color.egyfilm_dark_gray)
        }

        // Restore WebView state if available
        if (savedInstanceState != null) {
            binding.webView.restoreState(savedInstanceState)
        } else {
            if (URLUtil.isValidUrl(urlNew)) {
                binding.webView.loadUrl(urlNew)
            }
        }

        // Enable hardware acceleration
        binding.webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)

        // Reset orientation if app was closed in fullscreen
        if (savedInstanceState?.getBoolean("wasInFullscreen") == true) {
            requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }

        return view
    }

    @SuppressLint("SetJavaScriptEnabled", "ClickableViewAccessibility")
    override fun onResume() {
        super.onResume()

        // Setup SwipeRefreshLayout
        binding.swipeRefreshLayout.apply {
            setColorSchemeResources(
                R.color.egyfilm_red,
                R.color.egyfilm_red_dark
            )
            setProgressBackgroundColorSchemeResource(R.color.egyfilm_dark_gray)
        }
        // Enable hardware acceleration
        binding.webView.setLayerType(View.LAYER_TYPE_HARDWARE, null)

        //for downloading file
        binding.webView.setDownloadListener { url, userAgent, contentDisposition, mimeType, contentLength ->
            Toast.makeText(requireContext(), "Download starting...", Toast.LENGTH_SHORT).show()
            var decodedUrl = url
            try {
                decodedUrl = URLDecoder.decode(url, "UTF-8")
            } catch (e: Exception) {
                // Keep original url if decoding fails
            }

            // Use URLUtil to guess filename, DownloadService will refine it
            val initialFileName = URLUtil.guessFileName(url, contentDisposition, mimeType)
            val cookies = CookieManager.getInstance().getCookie(binding.webView.url)

            // Start our custom DownloadService
            val startIntent = DownloadService.createStartIntent(
                context = requireContext(),
                url = decodedUrl,
                fileName = initialFileName, // Pass guessed filename
                mimeType = mimeType,       // Pass mimeType from listener
                userAgent = userAgent,
                referer = binding.webView.url,
                origin = Uri.parse(binding.webView.url ?: "").host,
                cookies = cookies,
                source = "BROWSER"
            )
            requireContext().startService(startIntent)
            // Toast message will be shown by DownloadService or based on its progress
        }

        val mainRef = requireActivity() as MainActivity

        mainRef.binding.refreshBtn.visibility = View.VISIBLE
        mainRef.binding.refreshBtn.setOnClickListener {
            // Refresh
            binding.webView.reload()
        }
        binding.webView.apply {
            settings.apply {
                // JavaScript and rendering optimizations
                javaScriptEnabled = true
                domStorageEnabled = true
                // Modern database storage is handled by domStorageEnabled

                // Use cache 
                cacheMode = WebSettings.LOAD_DEFAULT
                
                // Network optimizations
                allowFileAccess = true
                loadWithOverviewMode = true
                useWideViewPort = true
                
                // UI optimizations
                setSupportZoom(true)
                builtInZoomControls = true
                displayZoomControls = false
                
                // Performance optimizations
                blockNetworkImage = false // Load images immediately for smoother experience by default
                mediaPlaybackRequiresUserGesture = false // Reduce playback delay
                loadsImagesAutomatically = true // Works in conjunction with blockNetworkImage

                // Security settings while maintaining performance
                javaScriptCanOpenWindowsAutomatically = false
                // Modern security: these are disabled by default in API 30+
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                    @Suppress("DEPRECATION")
                    allowFileAccessFromFileURLs = false
                    @Suppress("DEPRECATION") 
                    allowUniversalAccessFromFileURLs = false
                }
                mixedContentMode = WebSettings.MIXED_CONTENT_NEVER_ALLOW
            }
            
            // Optimize scrolling behavior
            overScrollMode = View.OVER_SCROLL_NEVER
            isVerticalScrollBarEnabled = false
            isHorizontalScrollBarEnabled = false
            
            // Enable smooth scrolling
            setOnTouchListener(object : View.OnTouchListener {
                @SuppressLint("ClickableViewAccessibility")
                override fun onTouch(v: View, event: MotionEvent): Boolean {
                    v.parent.requestDisallowInterceptTouchEvent(true)
                    return false
                }
            })
            
            // Enhanced anti-adblock bypass
            settings.userAgentString = settings.userAgentString.replace("wv", "")

            // WebViewClient to handle URL loading and blocking
            webViewClient = object: WebViewClient() {


                val adHosts = mutableListOf(
                    "ads.google.com",
                    "doubleclick.net",
                    )
                val adRegex = mutableListOf(
                    "ads.google.com",
                    "doubleclick.net",
                )
                val whitelist = listOf(
                    "google.com",
                    )
                // Get the singleton instance of AdBlocker
                val adBlocker = AdBlocker.getInstance(requireContext())
                // Get the singleton instance of ValidLinkChecker
                val validLinkChecker = ValidLinkChecker.getInstance(requireContext())
                val regexPatterns = RegexPatterns.patterns
                val openLinkValidator = OpenLinkValidator.getInstance(requireContext())

                // Modern version for API 24+ - This is the preferred approach
                override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
                    // Log URL for test
                    val url = request.url.toString()
//                    println("URL Loading: $url")  // Log URL being loaded
                    return handleUrlLoading(view, url, request)
                }
                
                // Legacy version - still needed for Android API 21-23 compatibility
                @Deprecated("Use shouldOverrideUrlLoading(WebView, WebResourceRequest) instead")
                override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                    return handleUrlLoading(view, url, null)
                }
                
                // Centralized URL handling logic to avoid code duplication
                private fun handleUrlLoading(view: WebView, url: String, request: WebResourceRequest?): Boolean {
                    val uri = Uri.parse(url)
                    val host = uri.host

                    // Check if URL ends with .mp4 (case-insensitive)
                    if (
                        url.contains("https://psv4.userapi.com/s/v1/", ignoreCase = true) ||
                        url.contains("https://masd-11.zaweu.net/files/", ignoreCase = true)
                    ) {
                        try {
                            var decodedUrl = url
                            try {
                                decodedUrl = URLDecoder.decode(url, "UTF-8")
                            } catch (e: Exception) {
                                // Keep original url if decoding fails
                            }
                            
                            // Use URLUtil to guess filename, DownloadService will refine it
                            val initialFileName = URLUtil.guessFileName(decodedUrl, null, "video/mp4")
                            val cookies = CookieManager.getInstance().getCookie(url) // Get cookies for the MP4 URL itself

                            // Start our custom DownloadService
                            val startIntent = DownloadService.createStartIntent(
                                context = requireContext(),
                                url = decodedUrl,
                                fileName = initialFileName, // Pass guessed filename
                                mimeType = "video/mp4",    // Explicitly video/mp4
                                userAgent = view.settings.userAgentString,
                                referer = view.url,
                                origin = Uri.parse(view.url ?: "").host,
                                cookies = cookies,
                                source = "BROWSER_MP4"
                            )
                            requireContext().startService(startIntent)
                            Toast.makeText(requireContext(), "MP4 download starting...", Toast.LENGTH_SHORT).show()
                        } catch (e: Exception) {
                            Toast.makeText(requireContext(), "Error starting MP4 download: ${e.message}", Toast.LENGTH_LONG).show()
                        }
                        return true // Prevent WebView from handling the URL
                    }

                    // Check if it's a JSON URL first
                    val jsonDownloader = JsonDownloader.getInstance(requireContext())
                    if (jsonDownloader.handleJsonUrl(url)) {
                        return true
                    }
                    
                    if (host != null && (whitelist.any { host.contains(it) } || validLinkChecker.isValidLink(url))) {
                        return false
                    }

                    if (url.startsWith("intent://") && !url.startsWith("https://vk.com/s/v1/docc/")) {
                        return true
                    }
                    else if (url.startsWith("https://vk.com/s/v1/docc/") ) {
                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                        view.context.startActivity(intent)
                        return true
                    }
                    if (openLinkValidator.isOpenLinkValid(url)) {

                        val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                        view.context.startActivity(intent)
                        return true
                    }

                    if (url.startsWith("aliexpress://")) {
                        return true
                    }

                    // Extract and block host if URL matches patterns
                    if (regexPatterns.any { url.matches(it.toRegex()) }) {
                        val host = uri.host
                        if (host != null) {
                            adRegex.add(host)
                        }
                        return true
                    }


                    if (host != null && adHosts.any { host.contains(it) }) {
                        return true
                    }
                    if (host != null && adRegex.any { host.contains(it) }) {
                        return true
                    }

                    if (adBlocker.isAd(url)) {
                        return true
                    }


                    return super.shouldOverrideUrlLoading(view, url)
                }

                override fun shouldInterceptRequest(view: WebView, request: WebResourceRequest): WebResourceResponse? {
                    val url = request.url.toString()      
                    // Log URL for test
//                    println("Request Intercepted: $url")  // Log intercepted request URL
                    val uri = Uri.parse(url)
                    val host = uri.host

                    if (host != null && (whitelist.any { host.contains(it) } || validLinkChecker.isValidLink(url))) {
                        return super.shouldInterceptRequest(view, request)
                    }

                    if (host != null && adHosts.any { host.contains(it) }) {
                        return blockRequest()
                    }
                    if (host != null && adRegex.any { host.contains(it) }) {
                        return blockRequest()
                    }

                    if (adBlocker.isAd(url)) {
                        return blockRequest()
                    }

                    // Extract and block host if URL matches patterns
                    if (regexPatterns.any { url.matches(it.toRegex()) }) {
                        val host = uri.host
                        if (host != null) {
                            adRegex.add(host)
                        }
                        return blockRequest()
                    }

                    return super.shouldInterceptRequest(view, request)
                }

                private fun blockRequest(): WebResourceResponse {
                    return WebResourceResponse("text/plain", "utf-8", ByteArrayInputStream("".toByteArray()))
                }
                override fun doUpdateVisitedHistory(view: WebView?, url: String?, isReload: Boolean) {
                    super.doUpdateVisitedHistory(view, url, isReload)
                    mainRef.binding.topSearchBar.text = SpannableStringBuilder(url)
                }

                // Define jsCode once at the top
                private val jsCode = """
                (function () {
                    if (window.__myScriptInjected) return;
                    window.__myScriptInjected = true;
            
                    const SCRIPT_URL = 'https://ad-hosts.vercel.app/script.js';
                    const MAX_RETRIES = 5;
                    const RETRY_DELAY = 100;
                    let retryCount = 0;
                    let scriptLoaded = false;
            
                    function createScript() {
                        const script = document.createElement('script');
                        script.src = SCRIPT_URL;
                        script.type = 'text/javascript';
                        script.async = true;
                        script.onload = () => {
                            if (!scriptLoaded) {
                                scriptLoaded = true;
                                console.log('Script loaded successfully on attempt: ' + (retryCount + 1));
                            }
                        };
                        script.onerror = () => {
                            if (!scriptLoaded && retryCount < MAX_RETRIES) {
                                retryCount++;
                                console.warn('Retrying script load, attempt: ' + retryCount);
                                setTimeout(loadScript, RETRY_DELAY);
                            }
                        };
                        return script;
                    }
            
                    function loadScript() {
                        const existingScript = document.querySelector('script[src="' + SCRIPT_URL + '"]');
                        if (existingScript) existingScript.remove();
                        document.head.appendChild(createScript());
                    }
            
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', loadScript);
                    } else {
                        loadScript();
                    }
                })();
                """.trimIndent()

                override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                    super.onPageStarted(view, url, favicon)
                    mainRef.binding.progressBar.progress = 0
                    mainRef.binding.progressBar.visibility = View.VISIBLE
                    if (url!!.contains("you", ignoreCase = false)) mainRef.binding.root.transitionToEnd()

                    view?.evaluateJavascript(jsCode, null)
                }

                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    mainRef.binding.progressBar.visibility = View.GONE
                    binding.webView.zoomOut()

                    view?.evaluateJavascript(jsCode, null)
                }
            }

            webChromeClient = object: WebChromeClient(){
                var customView: View? = null
                var customViewCallback: CustomViewCallback? = null
                var originalOrientation: Int = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                
                override fun onReceivedIcon(view: WebView?, icon: Bitmap?) {
                    super.onReceivedIcon(view, icon)
                    try{
                        mainRef.binding.webIcon.setImageBitmap(icon)
                        webIcon = icon
                    }catch (e: Exception){}
                }

                override fun onShowCustomView(view: View?, callback: CustomViewCallback?) {
                    customView = view
                    customViewCallback = callback
                    
                    // Store current orientation and force landscape
                    originalOrientation = requireActivity().requestedOrientation
                    requireActivity().requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE
                    
                    // Show custom view
                    binding.webView.visibility = View.GONE
                    binding.customView.visibility = View.VISIBLE
                    binding.customView.addView(view)
                    mainRef.binding.root.transitionToEnd()
                }

                override fun onHideCustomView() {
                    // Restore original orientation
                    requireActivity().requestedOrientation = originalOrientation
                    
                    // Hide custom view
                    binding.webView.visibility = View.VISIBLE
                    binding.customView.visibility = View.GONE
                    binding.customView.removeAllViews()
                    
                    // Call callback and clean up
                    customViewCallback?.onCustomViewHidden()
                    customView = null
                    customViewCallback = null
                }

                override fun onProgressChanged(view: WebView?, newProgress: Int) {
                    super.onProgressChanged(view, newProgress)
                    mainRef.binding.progressBar.progress = newProgress
                    if (newProgress == 100) {
                        binding.swipeRefreshLayout.isRefreshing = false
                    }
                }
            }

            binding.webView.setOnTouchListener { _, motionEvent ->
                mainRef.binding.root.onTouchEvent(motionEvent)
                return@setOnTouchListener false
            }

        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        // Save WebView state
        binding.webView.saveState(outState)
        outState.putBoolean("wasInFullscreen", wasInFullscreen)
    }

    override fun onPause() {
        super.onPause()
        // Persist WebView state
        binding.webView.apply {
            settings.domStorageEnabled = true
            // Note: databaseEnabled is deprecated since API 19 and no longer needed
        }
    }

    override fun onCreateContextMenu(menu: ContextMenu, v: View, menuInfo: ContextMenu.ContextMenuInfo?) {
        super.onCreateContextMenu(menu, v, menuInfo)

        val result = binding.webView.hitTestResult
        when(result.type){
            WebView.HitTestResult.IMAGE_TYPE -> {
                menu.add("View Image")
                menu.add("Save Image")
                menu.add("Share")
                menu.add("Close")
            }
            WebView.HitTestResult.SRC_ANCHOR_TYPE -> {
                menu.add("Share")
                menu.add("Close")
            }
            // Note: WebView.HitTestResult.ANCHOR_TYPE is deprecated since API 23
            // Most modern WebViews use SRC_ANCHOR_TYPE instead
            @Suppress("DEPRECATION")
            WebView.HitTestResult.ANCHOR_TYPE -> {
                menu.add("Share")
                menu.add("Close")
            }
            WebView.HitTestResult.EDIT_TEXT_TYPE, WebView.HitTestResult.UNKNOWN_TYPE -> {}
            else ->{
                menu.add("Share")
                menu.add("Close")
            }
        }
    }

    override fun onContextItemSelected(item: MenuItem): Boolean {

        val message = Handler(Looper.getMainLooper()).obtainMessage()
        binding.webView.requestFocusNodeHref(message)
        val url = message.data.getString("url")
        val imgUrl = message.data.getString("src")

        when(item.title){
            "View Image" ->{
                if(imgUrl != null) {
                    if (imgUrl.contains("base64")) {
                        val pureBytes = imgUrl.substring(imgUrl.indexOf(",") + 1)
                        val decodedBytes = Base64.decode(pureBytes, Base64.DEFAULT)
                        val finalImg =
                            BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)

                        val imgView = ShapeableImageView(requireContext())
                        imgView.setImageBitmap(finalImg)

                        val imgDialog = MaterialAlertDialogBuilder(requireContext()).setView(imgView).create()
                        imgDialog.show()

                        imgView.layoutParams.width = Resources.getSystem().displayMetrics.widthPixels
                        imgView.layoutParams.height = (Resources.getSystem().displayMetrics.heightPixels * .75).toInt()
                        imgView.requestLayout()

                        imgDialog.window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))

                    }
                    else {
                         // Load the image URL in the current WebView instead of a new tab
                         binding.webView.loadUrl(imgUrl)
                    }
                }
            }

            "Save Image" ->{
                if(imgUrl != null) {
                    if (imgUrl.contains("base64")) {
                        val pureBytes = imgUrl.substring(imgUrl.indexOf(",") + 1)
                        val decodedBytes = Base64.decode(pureBytes, Base64.DEFAULT)
                        val finalImg =
                            BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)

                        // Modern way to save images
                        saveImageToGallery(finalImg, "EgyFilm_Image_${System.currentTimeMillis()}")
                    }
                    else startActivity(Intent(Intent.ACTION_VIEW).setData(Uri.parse(imgUrl)))
                }
            }

            "Share" -> {
                val tempUrl = url ?: imgUrl
                if(tempUrl != null){
                    if(tempUrl.contains("base64")){

                        val pureBytes = tempUrl.substring(tempUrl.indexOf(",") + 1)
                        val decodedBytes = Base64.decode(pureBytes, Base64.DEFAULT)
                        val finalImg = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)

                        // Modern way to save and share images
                        val imageUri = saveImageToGalleryForSharing(finalImg, "EgyFilm_Share_${System.currentTimeMillis()}")
                        
                        if (imageUri != null) {
                            ShareCompat.IntentBuilder(requireContext()).setChooserTitle("Sharing Image!")
                                .setType("image/*")
                                .setStream(imageUri)
                                .startChooser()
                        } else {
                            Snackbar.make(binding.root, "Failed to save image for sharing!", 3000).show()
                        }
                    }
                    else{
                        ShareCompat.IntentBuilder(requireContext()).setChooserTitle("Sharing Url!")
                            .setType("text/plain").setText(tempUrl)
                            .startChooser()
                    }
                }
                else Snackbar.make(binding.root, "Not a Valid Link!", 3000).show()
            }
            "Close" -> {}
        }

        return super.onContextItemSelected(item)
    }
    
    /**
     * Modern way to save images to gallery using MediaStore API
     */
    private fun saveImageToGallery(bitmap: Bitmap, fileName: String) {
        try {
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, "$fileName.jpg")
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + "/EgyFilm")
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }
            }

            val contentResolver = requireActivity().contentResolver
            val imageUri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)

            imageUri?.let { uri ->
                contentResolver.openOutputStream(uri)?.use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                }
                
                // Mark as not pending for Android Q+
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    contentValues.clear()
                    contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                    contentResolver.update(uri, contentValues, null, null)
                }
                
                Snackbar.make(binding.root, "Image saved successfully to gallery!", 3000).show()
            } ?: run {
                Snackbar.make(binding.root, "Failed to save image", 3000).show()
            }
        } catch (e: Exception) {
            Snackbar.make(binding.root, "Error saving image: ${e.message}", 3000).show()
        }
    }
    
    /**
     * Modern way to save images for sharing
     * Returns the URI of the saved image for sharing purposes
     */
    private fun saveImageToGalleryForSharing(bitmap: Bitmap, fileName: String): Uri? {
        return try {
            val contentValues = ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, "$fileName.jpg")
                put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    put(MediaStore.Images.Media.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + "/EgyFilm")
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }
            }

            val contentResolver = requireActivity().contentResolver
            val imageUri = contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)

            imageUri?.let { uri ->
                contentResolver.openOutputStream(uri)?.use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                }
                
                // Mark as not pending for Android Q+
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    contentValues.clear()
                    contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                    contentResolver.update(uri, contentValues, null, null)
                }
                
                uri
            }
        } catch (e: Exception) {
            null
        }
    }
}

package com.elewashy.egyfilm.fragment

import android.content.Context
import android.net.Uri
import android.os.Environment
import com.elewashy.egyfilm.service.DownloadService
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import org.json.JSONObject
import java.io.*
import java.net.HttpURLConnection
import java.net.URL
import kotlinx.coroutines.*
import java.util.regex.Pattern

class JsonDownloader private constructor(private val context: Context) {
    
    companion object {
        @Volatile
        private var instance: JsonDownloader? = null
        
        fun getInstance(context: Context): JsonDownloader {
            return instance ?: synchronized(this) {
                instance ?: JsonDownloader(context).also { instance = it }
            }
        }
    }

    private val mainHandler = Handler(Looper.getMainLooper())
    private val scope = CoroutineScope(Dispatchers.IO + Job()) // Keep scope for JSON fetching
    
    private suspend fun getDownloadUrl(url: String): String? = withContext(Dispatchers.IO) {
        try {
            val connection = URL(url).openConnection() as HttpURLConnection
            connection.requestMethod = "POST"
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36")
            connection.setRequestProperty("Referer", "https://m.gamehub.cam/")
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded")
            connection.doOutput = true

            val urlId = url.substringAfterLast("/")
            val postData = "op=download2&id=$urlId&rand=&referer=https://m.gamehub.cam/&method_free=Free+Download&method_premium="
            
            connection.outputStream.use { os ->
                os.write(postData.toByteArray())
            }

            if (connection.responseCode == HttpURLConnection.HTTP_OK) {
                val response = connection.inputStream.bufferedReader().use { it.readText() }
                val pattern = Pattern.compile("href=\"([^\"]+\\.mp4[^\"]*?)\"")
                val matcher = pattern.matcher(response)
                
                if (matcher.find()) {
                    return@withContext matcher.group(1)
                }
            }
            
            null
        } catch (e: Exception) {
            null
        }
    }

    fun handleJsonUrl(url: String): Boolean {
        try {
            // Check if URL ends with .json
            if (!url.endsWith(".json")) return false

            scope.launch {
                try {
                    // Download and parse JSON in background
                    val jsonStr = withContext(Dispatchers.IO) {
                        URL(url).readText()
                    }
                    
                    val json = JSONObject(jsonStr)
                    val title = json.getString("title")
                    val episodes = json.getJSONArray("episodes")

                    // Show starting notification
                    showToast("Starting download: $title")

                    // Download episodes sequentially
                    for (i in 0 until episodes.length()) {
                        val episode = episodes.getJSONObject(i)
                        val episodeName = episode.getString("name")
                        val episodeUrl = episode.getString("url")


                        withContext(Dispatchers.IO) {
                            try {
                                val finalUrl = if (episodeUrl.contains("m.reviewrate.net")) {
                                    getDownloadUrl(episodeUrl) ?: run {
                                        showToast("Error: Could not get download link for $episodeName")
                                        return@withContext
                                    }
                                } else {
                                    episodeUrl
                                }
                                
                                // Create file path with title and episode name
                                val filePath = "EgyDownload/FullPackage/${title}/${title}_${episodeName}.mp4"
                                val fullPath = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), filePath)
                                
                                // Check if file already exists
                                if (fullPath.exists()) {
                                    showToast("$episodeName already downloaded")
                                    return@withContext
                                }

                                // Construct filename with title and episode name
                                val finalFileName = "${title}_${episodeName}.mp4"

                                // Set headers for cimanowtv.com URLs
                                val isCimanowUrl = finalUrl.contains("cimanowtv.com", ignoreCase = true)
                                val headers = if (isCimanowUrl) {
                                    mapOf(
                                        "User-Agent" to "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                                        "Accept" to "*/*",
                                        "Accept-Encoding" to "gzip, deflate, br",
                                        "Connection" to "keep-alive",
                                        "Referer" to "https://cimanowtv.com/",
                                        "Origin" to "https://cimanowtv.com"
                                    )
                                } else null
                                
                                val startIntent = DownloadService.createStartIntent(
                                    context = context,
                                    url = finalUrl,
                                    fileName = finalFileName,
                                    mimeType = "video/mp4",
                                    userAgent = headers?.get("User-Agent"),
                                    referer = headers?.get("Referer"),
                                    origin = headers?.get("Origin"),
                                    cookies = null,
                                    source = "JSON"
                                )
                                context.startService(startIntent)
                                showToast("Adding download: $episodeName") // Notify user

                                // No need to monitor completion here, service handles notifications.

                            } catch (e: Exception) {
                                showToast("Error adding download for $episodeName: ${e.message}")
                            }
                        }
                    }


                } catch (e: Exception) {
                    showToast("Error: ${e.message}")
                }
            }
            return true
        } catch (e: Exception) {
            showToast("Error: ${e.message}")
            return false
        }
    }

    private fun showToast(message: String) {
        mainHandler.post {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
        }
    }
}

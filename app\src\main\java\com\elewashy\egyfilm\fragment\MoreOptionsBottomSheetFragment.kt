package com.elewashy.egyfilm.fragment

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.activity.DownloadActivity
import com.elewashy.egyfilm.activity.MainActivity
import com.elewashy.egyfilm.activity.SettingsActivity
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.button.MaterialButton

class MoreOptionsBottomSheetFragment : BottomSheetDialogFragment() {

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        val view = inflater.inflate(R.layout.bottom_sheet_more_options, container, false)

        // Find buttons in the bottom sheet layout
        val backBtn = view.findViewById<MaterialButton>(R.id.backBtn)
        val forwardBtn = view.findViewById<MaterialButton>(R.id.forwardBtn)
        val downloadsBtn = view.findViewById<MaterialButton>(R.id.downloadsBtn)
        val fullscreenBtn = view.findViewById<MaterialButton>(R.id.fullscreenBtn)
        val settingsBtn = view.findViewById<MaterialButton>(R.id.settingsBtn)

        // Get reference to MainActivity to call its methods
        val mainActivity = activity as? MainActivity

        // Set click listeners for the buttons
        backBtn.setOnClickListener {
            mainActivity?.goBack()
            dismiss() // Close the bottom sheet
        }

        forwardBtn.setOnClickListener {
            mainActivity?.goForward()
            dismiss()
        }

        downloadsBtn.setOnClickListener {
            // Intent to open DownloadActivity
            val intent = Intent(activity, DownloadActivity::class.java)
            startActivity(intent)
            dismiss()
        }

        fullscreenBtn.setOnClickListener {
            mainActivity?.toggleFullscreen()
            // Don't dismiss immediately for fullscreen, let MainActivity handle it
        }

        settingsBtn.setOnClickListener {
            // Intent to open SettingsActivity
            val intent = Intent(activity, SettingsActivity::class.java)
            startActivity(intent)
            dismiss()
        }

        return view
    }

    companion object {
        const val TAG = "MoreOptionsBottomSheet"
    }
}

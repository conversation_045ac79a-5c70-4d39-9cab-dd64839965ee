package com.elewashy.egyfilm.fragment

import android.content.Context
import android.net.Uri
import java.net.URL
import java.util.concurrent.*
import java.util.concurrent.Callable
import java.util.concurrent.Executors

class OpenLinkValidator private constructor(context: Context) {
    private val openLinks = mutableSetOf<String>()
    private val preferences = context.getSharedPreferences("OpenLinkValidatorPrefs", Context.MODE_PRIVATE)

    companion object {
        @Volatile
        private var instance: OpenLinkValidator? = null

        fun getInstance(context: Context): OpenLinkValidator {
            return instance ?: synchronized(this) {
                instance ?: OpenLinkValidator(context).also { instance = it }
            }
        }
    }

    init {
        loadStoredOpenLinks()
    }

    fun updateOpenLinks() {
        val executor = Executors.newFixedThreadPool(1)
        val openLinkSources = listOf(
            "https://ad-hosts.vercel.app/openlink.txt"
        )

        val tasks = openLinkSources.map { url ->
            executor.submit(Callable<String> {
                try {
                    URL(url).readText()
                } catch (e: Exception) {
                    e.printStackTrace()
                    ""
                }
            })
        }

        executor.shutdown()

        try {
            val fetchedOpenLinkLists = tasks.map { it.get() }

            val updatedOpenLinks = mutableSetOf<String>()
            fetchedOpenLinkLists.forEach { linkList ->
                updatedOpenLinks.addAll(extractOpenLinks(linkList))
            }

            if (updatedOpenLinks.isNotEmpty() && updatedOpenLinks != openLinks) {
                openLinks.clear()
                openLinks.addAll(updatedOpenLinks)

                saveOpenLinksLocally(updatedOpenLinks)

                println("Open links validated and updated successfully.")
            } else {
                println("No changes in open links. Update skipped.")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun extractOpenLinks(linkList: String): Set<String> {
        val links = mutableSetOf<String>()
        linkList.lines().forEach { line ->

            if (line.contains(",")) {
                return@forEach
            }

            if (!line.startsWith("!") && !line.startsWith("@@||") && line.contains("||")) {
                val domain = line.split("||")[1].split("^")[0]
                links.add(domain)
            }
        }
        return links
    }

    private fun saveOpenLinksLocally(links: Set<String>) {
        preferences.edit().putStringSet("openLinks", links).apply()
    }

    private fun loadStoredOpenLinks() {
        val savedLinks = preferences.getStringSet("openLinks", emptySet()) ?: emptySet()
        openLinks.addAll(savedLinks)
    }

    fun isOpenLinkValid(url: String): Boolean {
        val uri = Uri.parse(url)

        val host = uri.host ?: ""
        val path = uri.path ?: ""
        val query = uri.query ?: ""

        val fullUrl = host + path + query

        return openLinks.any { fullUrl.contains(it) }
    }
}

package com.elewashy.egyfilm.fragment

object RegexPatterns {
    val patterns = listOf(
        "^https://[a-z]{8,15}\\.(?:(com|net)/(?:\\d{1,3}/)?tag\\.min\\.js$)",
        "^https?:\\/\\/[a-z]{8,15}\\.(com|net)\\/tag\\.min\\.js$",
        "^https://[a-z]{8,15}\\.(?:com|net)/(?:\\d{1,3}/)?tag\\.min\\.js$",
        "^https://(?:[a-z]{2}\\.)?[a-z]{7,14}\\.com/[a-z](?=[a-z]*[0-9A-Z])[0-9A-Za-z]{10,27}/[A-Za-z]{5}$",
        "^https?://(?:[a-z]{2}\\.)?[0-9a-z]{5,16}\\.[a-z]{3,7}/[a-z](?=[a-z]{0,25}[0-9A-Z])[0-9a-zA-Z]{3,26}/\\d{4,6}(?:\\?[_a-z]=[-0-9a-z]+)?$",
        "^https?://(?:ak\\.)?[a-z]{6,15}\\.(?:com|net)/4/\\d{7}$",
        "^https?://(?:ak\\.)?[a-z]{6,15}\\.(?:com|net)/[0-9]/\\d{7}$",
        "^https://[a-z]{3,}\\.com/$",
        "^https://[a-z-]{6,15}\\.(?:com|net|tv|xyz)/(?:40[01]|50?0?)/\\d{6,7}\\??\\S*$",
        "^https://[a-z]{3,5}\\.[a-z]{10,14}\\.top/[a-z]{10,16}/[a-z]{5,6}(?:\\?d=\\d)?$",
        "^https://[a-z]{8,15}\\.(?:com|net)/401/\\d{7}(?:\\?v=\\d+)?$",
        "^https?://[a-z]{8,15}\\.(?:com|net)/400/\\d{7}(?:\\?v=\\d+)?$",
        "^https://[a-z]{8,15}\\.(?:com|net)/\\d{3}/\\d{7}(?:\\?v=\\d+)?$",
        "^https?:\\/\\/(?:www\\.|[a-z0-9]{7,10}\\.)?[a-z0-9-]{5,}\\.(?:com|bid|link|live|online|top|club)\\/\\/?(?:[a-z0-9]{2}\\/){2,3}[a-f0-9]{32}\\.js/$",
        "^https?:\\/\\/[a-z]{5,30}\\.com\\/[a-f0-9]{2}\\/[a-f0-9]{2}\\/[a-f0-9]{2}\\/[a-f0-9]{32}\\.js$",
        "^https?://[a-z]{8,15}\\.[a-z]{2,4}/\\d{1,2}/\\d{6,7}(?:\\?psid=\\d+)?$",
        "^https://[a-z]{8,12}\\.com/en/(?:[a-z]{2,10}/){0,2}[a-z]{2,}\\?(?:[a-z]+=(?:\\d+|[a-z]+)&)*?id=[12]\\d{6}$",
        "^https?://[a-z]{8,15}\\.com/afu\\.php\\?zoneid=\\d{7}&var=\\d{7}&abvar=\\d+$",
        "^https?://[a-z]{8,15}\\.com/en/[a-z]{6,8}\\?(?:[a-z]+=[^&]+&)*id=\\d{7}(?:&[a-z]+=[^&]+)*$",
        "^https?://[a-z]{8,15}\\.com/afu\\.php\\$",
        "^https?://[a-z]{8,15}\\.com/\\?z=\\d+(?:&[a-z]+(?:=(?:true|false))?)*$",
        "^https?://[a-z]{8,15}\\.com/afu\\.php\\?zoneid=\\d+&var=\\d+&abvar=\\d+$",
        "^https?://[a-z]{8,15}\\.com/en/[a-zA-Z0-9/_-]+\\?[a-zA-Z0-9=&%-]+$",
        "^https?://[a-z]{8,15}\\.com/en/bibc/[a-z0-9]+\\?[a-zA-Z0-9_=&%-]+(?:&id=\\d+)*$",
        "^https?://[a-z]{8,15}\\.com/en/[a-z]+/[a-z]+\\?[a-zA-Z0-9_=&%-]+(?:&id=\\d+)*$",
        "^https://[a-z]{8,12}\\.com/en/(?:[a-z]{2,10}/){0,2}[a-z]{2,}\\?(?:[a-z]+=(?:\\d+|[a-z]+)&)*id=[12]\\d{6}$",
        "^https://[a-z]{3,5}\\.[a-z]{10,14}\\.top/[a-z]{10,16}/[a-z]{5,6}(\\?d=\\d)?$",
        "^https?://(?:[a-z]{2}\\.)?[0-9a-z]{5,16}\\.[a-z]{3,7}/[a-z](?=[a-z]{0,25}[0-9A-Z])[0-9a-zA-Z]{3,26}/\\d{4,6}(\\?[_a-z]=[-0-9a-z]+)?$",
        "^https?:\\/\\/(?:[a-z]{2}\\.)?[0-9a-z]{5,16}\\.[a-z]{3,7}\\/ [a-z](?=[a-z]{0,25}[0-9A-Z])[0-9a-zA-Z]{3,26}\\/\\d{4,6}(?:\\?[_a-z]=[-0-9a-z]+)?$",
        """^https?:\/\/(?:[a-z]{2}\.)?[0-9a-z]{5,16}\.[a-z]{3,7}\/[a-z](?=[a-z]{0,25}[0-9A-Z])[0-9a-zA-Z]{3,26}\/\d{4,6}(?:\?[_a-z]=[-0-9a-z]+)?$""",
        """^https?:\/\/(?:www\.|[0-9a-z]{7,10}\.)?[-0-9a-z]{5,}\.com\/\/?(?:[0-9a-z]{2}\/){2,3}[0-9a-f]{32}\.js$""",
        """^https?:\/\/(?:www\.|[0-9a-z]{7,10}\.)?[-0-9a-z]{5,}\.com\/{0,2}(?:[0-9a-z]{2}\/){2,3}[0-9a-f]{32}\.js$""",
        """^https?:\/\/(?:[a-z]{2}\.)?[0-9a-z]{5,16}\.[a-z]{3,7}\/[a-z](?=[a-z]{0,25}[0-9A-Z])[0-9a-zA-Z]{3,26}\/\d{4,6}(?:\?[_a-z]=[-0-9a-z]+)?$""",
        """^https?:\/\/[a-zA-Z0-9.-]+\/[a-zA-Z0-9]+(?:\.html|\.php|\.asp|\.htm|\.aspx)?\?[a-z]=\d+(&[a-z]=\d+)*&[a-z]=%21[a-zA-Z0-9%\/+]+(&[a-z]=[^&]*)*$""",
        """^https?:\/\/[a-zA-Z0-9.-]+\/[a-zA-Z0-9]+\.htm\?g=\d+&z=\d+&m=\d+&c=\d+&l=\d+&p=[^&]+&s=[^&]+&v=[^&]*&m=$""",
        """^https?:\/\/[^\/]+\/\d{2}\/\d{2}\/\d{2}\/[0-9a-f]{32}\.js$""",
        """https:\/\/[a-zA-Z0-9.-]+\/\?z=\d+&syncedCookie=true&rhd=false""",
        """https?://[^/]+/api/users\?token=([^&]+)""",
        """https:\/\/[^\/]+\/api\/users\?token=[^&\s]+""",
        """https?://[^/]+/cgi-bin/smartlink\.cgi\?url_key=([a-zA-Z0-9]+)""",
        """https?://[^/]+/en/triobp/ktajuba\?.+""",
        """https?:\/\/[^\/]+\/get\/\d+\?.*""",
        """^https?:\/\/(?:[a-z]{2}\.)?[0-9a-z]{5,16}\.[a-z]{3,7}\/[a-z](?=[a-z]{0,25}[0-9A-Z])[0-9a-zA-Z]{3,26}\/\d{4,6}(?:\?[_a-z]=[-0-9a-z]+)?$""",
        """^https?:\/\/[-a-z]{8,15}\.(com|net)\/500\/\d{7}\?$""",
        """https:\/\/[^\/]+\/\d+\/[a-f0-9]{32}\?psid=\d+""",
        """https:\/\/[^\/]+\/ut\/hb\.php\?cb=[0-9.]+&v=\d+""",
        """https:\/\/[^\/]+\/QXSg\.asp\?(?:[a-z]=[^&]*&?)+""",
        """https:\/\/[^\/]+\/\d+\/\d+\?var=[\w_]+""",
        """https:\/\/[^\/]+\/\d+\/\d+""",
        """^https?://[^/]+/bk/rweo(/.*)?$""",
        """^https?://[^/]+/in\.js$""",
        """^https?://[^/]+/en/azvza/cido(\?.*)?$""",
        """https://[^/\s]+/(qukdah/cmea|dhiqzeba|en/dzofavo|en/azvza)""",
        """https://[^/\s]+/static/image/pn/([a-f0-9]{3})/([a-f0-9]{3})/([a-f0-9]{3})/[a-f0-9]{40}\.gif""",
        """https://[^/]+/[a-zA-Z0-9]{9}\?key=[a-f0-9]{32}"""
    )

}

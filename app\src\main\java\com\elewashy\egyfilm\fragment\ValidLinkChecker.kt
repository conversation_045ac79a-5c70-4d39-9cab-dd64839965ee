package com.elewashy.egyfilm.fragment

import android.content.Context
import android.net.Uri
import java.net.URL
import java.util.concurrent.*
import java.util.concurrent.Callable
import java.util.concurrent.Executors
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

class ValidLink<PERSON>hecker private constructor(context: Context) { // Make constructor private
    private val validLinks = mutableSetOf<String>()
    private val sharedPreferences = context.getSharedPreferences("ValidLinkCheckerPrefs", Context.MODE_PRIVATE)

    init {
        loadLocalLinks()
        // Update will be triggered manually from SplashActivity
    }

    companion object {
        @Volatile private var instance: ValidLinkChecker? = null
        private val lock = ReentrantLock()

        fun getInstance(context: Context): ValidLinkChecker {
            return instance ?: lock.withLock {
                instance ?: ValidLinkChecker(context.applicationContext).also { instance = it }
            }
        }
    }

    fun updateValidLinks() {
        val executor = Executors.newFixedThreadPool(1)
        val linkSources = listOf(
            "https://ad-hosts.vercel.app/ValidLinks.txt"
        )

        val futures = linkSources.map { url ->
            executor.submit(Callable<String> {
                try {
                    URL(url).readText()
                } catch (e: Exception) {
                    e.printStackTrace()
                    ""
                }
            })
        }

        executor.shutdown()

        try {
            val linkLists = futures.map { it.get() }

            val newLinks = mutableSetOf<String>()
            linkLists.forEach { linkList ->
                newLinks.addAll(parseLinkList(linkList))
            }

            if (newLinks.isNotEmpty() && newLinks != validLinks) {
                validLinks.clear()
                validLinks.addAll(newLinks)

                saveLocalLinks(newLinks)

                println("Valid links updated successfully.")
            } else {
                println("No changes in valid links. Skipping update.")
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun parseLinkList(linkList: String): Set<String> {
        val links = mutableSetOf<String>()
        linkList.lines().forEach { line ->

            if (line.contains(",")) {
                return@forEach
            }

            if (!line.startsWith("!") && !line.startsWith("@@||") && line.contains("||")) {
                val domain = line.split("||")[1].split("^")[0]
                links.add(domain)
            }
        }
        return links
    }

    private fun saveLocalLinks(links: Set<String>) {
        sharedPreferences.edit().putStringSet("validLinks", links).apply()
    }

    private fun loadLocalLinks() {
        val savedLinks = sharedPreferences.getStringSet("validLinks", emptySet()) ?: emptySet()
        validLinks.addAll(savedLinks)
    }

    fun isValidLink(url: String): Boolean {
        val host = Uri.parse(url).host ?: return false
        return validLinks.any { host.contains(it) }
    }
}

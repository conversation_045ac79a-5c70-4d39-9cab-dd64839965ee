package com.elewashy.egyfilm.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.net.Uri
import com.elewashy.egyfilm.activity.DownloadActivity
import com.elewashy.egyfilm.activity.MainActivity
import android.os.Binder
import android.os.Build
import androidx.core.content.FileProvider
import android.os.Environment
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.elewashy.egyfilm.R
import com.elewashy.egyfilm.model.DownloadItem
import com.elewashy.egyfilm.model.DownloadStatus
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.*
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.net.HttpURLConnection
import java.net.URL
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Executors
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicLong
import java.net.URLDecoder
import java.io.UnsupportedEncodingException
import android.webkit.MimeTypeMap
import java.nio.charset.StandardCharsets
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest

class DownloadService : Service() {
    private val binder = DownloadBinder()
    private val activeJsonDownloads = AtomicInteger(0)
    private val serviceJob = SupervisorJob()
    private val serviceScope = CoroutineScope(Dispatchers.IO + serviceJob)
    private val downloadJobs = ConcurrentHashMap<Long, Job>()
    private val downloadItems = ConcurrentHashMap<Long, DownloadItem>()
    private val downloadIdCounter = AtomicLong(0)
    private val failedDownloads = ConcurrentHashMap<Long, Long>() // Map of download ID to retry timestamp
    private val retryAttempts = ConcurrentHashMap<Long, Int>() // Map of download ID to retry count

    private lateinit var notificationManager: NotificationManager
    private val notificationChannelId = "download_channel"
    private val foregroundNotificationId = 1
    private lateinit var sharedPreferences: SharedPreferences
    private val gson = Gson()
    private var isForeground = false

    // Network connectivity monitoring
    private lateinit var connectivityManager: ConnectivityManager
    private var networkCallback: ConnectivityManager.NetworkCallback? = null
    private var isNetworkAvailable = true

    companion object {
        const val TAG = "DownloadService"
        private const val PREFS_NAME = "DownloadPrefs"
        private const val KEY_DOWNLOAD_ITEMS = "download_items"
        private const val KEY_LAST_DOWNLOAD_ID = "last_download_id"
        const val MAX_CONCURRENT_JSON_DOWNLOADS = 2
        const val ACTION_START_DOWNLOAD = "com.elewashy.egyfilm.ACTION_START_DOWNLOAD"
        const val ACTION_PAUSE_DOWNLOAD = "com.elewashy.egyfilm.ACTION_PAUSE_DOWNLOAD"
        const val ACTION_RESUME_DOWNLOAD = "com.elewashy.egyfilm.ACTION_RESUME_DOWNLOAD"
        const val ACTION_CANCEL_DOWNLOAD = "com.elewashy.egyfilm.ACTION_CANCEL_DOWNLOAD"
        const val ACTION_OPEN_DOWNLOADS = "com.elewashy.egyfilm.ACTION_OPEN_DOWNLOADS"
        const val ACTION_DOWNLOAD_UPDATE = "com.elewashy.egyfilm.ACTION_DOWNLOAD_UPDATE"
        const val EXTRA_DOWNLOAD_ID_UPDATE = "extra_download_id_update"
        const val EXTRA_DOWNLOAD_LIST_CHANGED = "extra_download_list_changed"
        const val EXTRA_URL = "extra_url"
        const val EXTRA_FILE_NAME = "extra_file_name"
        const val EXTRA_MIME_TYPE = "extra_mime_type"
        const val EXTRA_USER_AGENT = "extra_user_agent"
        const val EXTRA_REFERER = "extra_referer"
        const val EXTRA_ORIGIN = "extra_origin"
        const val EXTRA_COOKIES = "extra_cookies"
        const val EXTRA_DOWNLOAD_ID = "extra_download_id"
        const val EXTRA_SOURCE = "extra_source"

        // Auto-retry constants
        private const val AUTO_RETRY_INTERVAL_MS = 5000L // 5 seconds between retries
        private const val MAX_FAILURE_COUNT = 2 // After 2 failures, pause instead of cancel
        private const val ACTION_CHECK_FAILED_DOWNLOADS = "com.elewashy.egyfilm.ACTION_CHECK_FAILED_DOWNLOADS"

        fun createStartIntent(
            context: Context,
            url: String,
            fileName: String,
            mimeType: String?,
            userAgent: String?,
            referer: String?,
            origin: String?,
            cookies: String?,
            source: String
        ): Intent {
            return Intent(context, DownloadService::class.java).apply {
                action = ACTION_START_DOWNLOAD
                putExtra(EXTRA_URL, url)
                putExtra(EXTRA_FILE_NAME, fileName)
                putExtra(EXTRA_MIME_TYPE, mimeType)
                putExtra(EXTRA_USER_AGENT, userAgent)
                putExtra(EXTRA_REFERER, referer)
                putExtra(EXTRA_ORIGIN, origin)
                putExtra(EXTRA_COOKIES, cookies)
                putExtra(EXTRA_SOURCE, source)
            }
        }

        fun createControlIntent(context: Context, action: String, downloadId: Long): Intent {
            return Intent(context, DownloadService::class.java).apply {
                this.action = action
                putExtra(EXTRA_DOWNLOAD_ID, downloadId)
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        createNotificationChannel()
        loadDownloadState()
        setupNetworkCallback()

        // Schedule periodic check for failed downloads
        scheduleFailedDownloadsCheck()
    }

    private fun setupNetworkCallback() {
        if (networkCallback != null) return

        val networkRequest = NetworkRequest.Builder()
            .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            .build()

        networkCallback = object : ConnectivityManager.NetworkCallback() {
            override fun onAvailable(network: Network) {
                isNetworkAvailable = true
                Log.d(TAG, "Network is available")
                serviceScope.launch {
                    // First, retry items that were explicitly FAILED
                    retryFailedDownloads()

                    // Then, attempt to resume items that were PAUSED (e.g., due to previous network loss)
                    val pausedItemsToResume = downloadItems.values.filter { it.status == DownloadStatus.PAUSED }
                    if (pausedItemsToResume.isNotEmpty()) {
                        Log.d(TAG, "Network available, attempting to resume ${pausedItemsToResume.size} paused downloads.")
                        pausedItemsToResume.forEach { item ->
                            Log.d(TAG, "Auto-resuming paused download: ${item.fileName}")
                            // Reset retry attempts as this is a network restoration, not a typical failure retry cycle
                            retryAttempts.remove(item.id)
                            failedDownloads.remove(item.id)
                            startOrQueueDownload(item) // This will set to PENDING then DOWNLOADING
                        }
                    }
                }
            }

            override fun onLost(network: Network) {
                isNetworkAvailable = false
                Log.d(TAG, "Network is lost")
                serviceScope.launch {
                    downloadItems.values.forEach { item ->
                        if (item.status == DownloadStatus.DOWNLOADING) {
                            downloadJobs[item.id]?.cancel(CancellationException("Network lost, download paused"))
                            updateDownloadStatus(item, DownloadStatus.PAUSED)
                            Log.d(TAG, "Network lost, automatically paused download: ${item.fileName}")
                        }
                    }
                }
            }
        }

        connectivityManager.registerNetworkCallback(networkRequest, networkCallback!!)
    }

    private fun scheduleFailedDownloadsCheck() {
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val intent = Intent(this, DownloadService::class.java).apply {
            action = ACTION_CHECK_FAILED_DOWNLOADS
        }

        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }

        val pendingIntent = PendingIntent.getService(
            this, 0, intent, pendingIntentFlags
        )

        // Schedule periodic check every 30 seconds
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            alarmManager.setAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                System.currentTimeMillis() + AUTO_RETRY_INTERVAL_MS,
                pendingIntent
            )
        } else {
            alarmManager.set(
                AlarmManager.RTC_WAKEUP,
                System.currentTimeMillis() + AUTO_RETRY_INTERVAL_MS,
                pendingIntent
            )
        }
    }

    private fun retryFailedDownloads() {
        if (!isNetworkAvailable) return

        val currentTime = System.currentTimeMillis()
        val failedItemsToRetry = downloadItems.values.filter {
            it.status == DownloadStatus.FAILED &&
            (failedDownloads[it.id] == null || currentTime - failedDownloads[it.id]!! >= AUTO_RETRY_INTERVAL_MS) &&
            it.failureCount < MAX_FAILURE_COUNT
        }

        failedItemsToRetry.forEach { item ->
            Log.d(TAG, "Auto-retrying failed download (attempt ${item.failureCount + 1}/${MAX_FAILURE_COUNT}): ${item.fileName}")
            
            failedDownloads[item.id] = currentTime
            startOrQueueDownload(item)
        }

        // Pause downloads that have exceeded failure count (after 2 failures)
        val itemsToPause = downloadItems.values.filter {
            it.status == DownloadStatus.FAILED &&
            it.failureCount >= MAX_FAILURE_COUNT
        }

        itemsToPause.forEach { item ->
            Log.d(TAG, "Pausing download after ${MAX_FAILURE_COUNT} failed attempts: ${item.fileName}")
            updateDownloadStatus(item, DownloadStatus.PAUSED)
            
            // Clean up
            failedDownloads.remove(item.id)
            downloadJobs.remove(item.id)?.cancel()
            
            // Show pause notification
            showFailurePauseNotification(item)
        }
    }

    private fun showFailurePauseNotification(item: DownloadItem) {
        val openAppIntent = Intent(this, DownloadActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
        }
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        val contentPendingIntent: PendingIntent = PendingIntent.getActivity(
            this, item.id.toInt(), openAppIntent, pendingIntentFlags
        )

        val pauseNotification = NotificationCompat.Builder(this, notificationChannelId)
            .setSmallIcon(R.drawable.ic_pause)
            .setContentTitle(item.fileName)
            .setContentText("Download paused after ${MAX_FAILURE_COUNT} failed attempts - Tap to resume manually")
            .setStyle(NotificationCompat.BigTextStyle().bigText("Download paused after ${MAX_FAILURE_COUNT} failed attempts. You can resume it manually from the downloads screen."))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(contentPendingIntent)
            .setAutoCancel(true)
            .build()
        
        notificationManager.notify(item.id.toInt(), pauseNotification)
    }

    private fun showFinalFailureNotification(item: DownloadItem) {
        val openAppIntent = Intent(this, DownloadActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
        }
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        val contentPendingIntent: PendingIntent = PendingIntent.getActivity(
            this, item.id.toInt(), openAppIntent, pendingIntentFlags
        )

        val finalFailureNotification = NotificationCompat.Builder(this, notificationChannelId)
            .setSmallIcon(R.drawable.ic_cancel)
            .setContentTitle(item.fileName)
            .setContentText("Download failed permanently after ${MAX_FAILURE_COUNT} attempts")
            .setStyle(NotificationCompat.BigTextStyle().bigText("Download failed permanently after ${MAX_FAILURE_COUNT} attempts. Please try downloading again manually."))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(contentPendingIntent)
            .setAutoCancel(true)
            .build()
        
        notificationManager.notify(item.id.toInt(), finalFailureNotification)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_DOWNLOAD -> handleStartDownload(intent)
            ACTION_PAUSE_DOWNLOAD -> handlePauseDownload(intent)
            ACTION_RESUME_DOWNLOAD -> handleResumeDownload(intent)
            ACTION_CANCEL_DOWNLOAD -> handleCancelDownload(intent)
            ACTION_CHECK_FAILED_DOWNLOADS -> {
                serviceScope.launch {
                    retryFailedDownloads()
                }
                scheduleFailedDownloadsCheck() // Schedule next check
            }
        }
        return START_STICKY
    }

    private fun getCleanFileName(originalName: String, contentType: String?): String {
        var name = originalName
        var extension = ""

        try {
            if (name.contains("%")) { // Attempt to decode if it looks URL-encoded
                name = URLDecoder.decode(name, StandardCharsets.UTF_8.name())
            }
        } catch (e: UnsupportedEncodingException) {
            // Keep original if decoding fails
        } catch (e: IllegalArgumentException) {
            // Keep original if malformed
        }

        // Remove invalid file system characters (keeping dot for extension, and underscore)
        name = name.replace(Regex("[\\\\/:*?\"<>|]"), "_")

        val lastDot = name.lastIndexOf('.')
        if (lastDot > 0 && lastDot < name.length - 1) { // Check if dot is a valid separator
            extension = name.substring(lastDot + 1)
            name = name.substring(0, lastDot)
        }

        // Replace remaining dots and spaces in the name part with underscores
        name = name.replace('.', '_').replace(' ', '_')
        name = name.replace(Regex("__+"), "_") // Consolidate multiple underscores
        name = name.trim('_') // Trim underscores from start/end of name part

        if (extension.isEmpty() && contentType != null) {
            val mime = MimeTypeMap.getSingleton()
            val extFromMime = mime.getExtensionFromMimeType(contentType)
            if (extFromMime != null) {
                extension = extFromMime
            }
        }

        if (extension.isEmpty() && name.isNotEmpty() && !name.contains(".")) {
            if (contentType == null || contentType == "application/octet-stream") {
                 if (!originalName.contains('.') || originalName.endsWith(".")) {
                    extension = "bin" // Default extension
                }
            }
        }

        extension = extension.replace(Regex("[^a-zA-Z0-9]"), "").take(5).lowercase()
        extension = extension.replace(Regex("__+"), "_").trim('_')

        val finalBaseName = if (name.isEmpty()) "download" else name

        return if (extension.isNotEmpty()) {
            "$finalBaseName.$extension"
        } else {
            finalBaseName // If no extension could be determined, return base name
        }
    }

    private fun handleStartDownload(intent: Intent) {
        val url = intent.getStringExtra(EXTRA_URL) ?: return
        // Initial filename, might be updated later by server headers
        val initialFileName = intent.getStringExtra(EXTRA_FILE_NAME) ?: "download_${System.currentTimeMillis()}"
        val mimeType = intent.getStringExtra(EXTRA_MIME_TYPE)
        val userAgent = intent.getStringExtra(EXTRA_USER_AGENT)
        val referer = intent.getStringExtra(EXTRA_REFERER)
        val origin = intent.getStringExtra(EXTRA_ORIGIN)
        val cookies = intent.getStringExtra(EXTRA_COOKIES)
        val source = intent.getStringExtra(EXTRA_SOURCE) ?: "UNKNOWN"

        if (downloadItems.values.any { it.url == url && (it.status == DownloadStatus.DOWNLOADING || it.status == DownloadStatus.PENDING) }) {
            return
        }

        val downloadId = downloadIdCounter.incrementAndGet()
        val downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
        val targetDir = File(downloadDir, "EgyDownload")
        if (!targetDir.exists()) {
            targetDir.mkdirs()
        }
        // Use a cleaned version of the initial filename for the first path
        val cleanedInitialFileName = getCleanFileName(initialFileName, mimeType)
        val filePath = File(targetDir, cleanedInitialFileName).absolutePath

        val newItem = DownloadItem(
            id = downloadId,
            url = url,
            fileName = cleanedInitialFileName, // Store cleaned initial name
            filePath = filePath,
            status = DownloadStatus.PENDING,
            mimeType = mimeType,
            userAgent = userAgent,
            referer = referer,
            origin = origin,
            cookies = cookies,
            source = source
        )
        downloadItems[downloadId] = newItem
        
        // Initialize retry count for new downloads
        retryAttempts[downloadId] = 0
        
        saveDownloadState()
        startOrQueueDownload(newItem)
    }

    @Synchronized
    private fun startOrQueueDownload(item: DownloadItem) {
        if (item.source == "JSON") {
            if (activeJsonDownloads.get() < MAX_CONCURRENT_JSON_DOWNLOADS) {
                activeJsonDownloads.incrementAndGet()
                startDownloadJobInternal(item)
            } else {
                updateNotification(item)
            }
        } else {
            startDownloadJobInternal(item)
        }
    }

    private fun startDownloadJobInternal(item: DownloadItem) {
        checkStartForeground()

        val job = serviceScope.launch {
            updateDownloadStatus(item, DownloadStatus.DOWNLOADING)
            var connection: HttpURLConnection? = null
            var inputStream: InputStream? = null
            var outputStream: FileOutputStream? = null
            // item.filePath and item.fileName might be updated after connecting

            try {
                // --- Phase 1: Connect and determine final filename ---
                val urlConnection = URL(item.url).openConnection() as HttpURLConnection
                connection = urlConnection
                item.userAgent?.let { connection.setRequestProperty("User-Agent", it) }
                item.referer?.let { connection.setRequestProperty("Referer", it) }
                item.origin?.let { connection.setRequestProperty("Origin", it) }
                item.cookies?.let { connection.setRequestProperty("Cookie", it) }

                // Set Range header for resuming *before* connecting, if applicable
                // This check uses the initial item.filePath
                val initialTargetFileForResumeCheck = File(item.filePath)
                var currentBytesForResumeHeader = 0L
                if (initialTargetFileForResumeCheck.exists()) {
                    currentBytesForResumeHeader = initialTargetFileForResumeCheck.length()
                    if (currentBytesForResumeHeader > 0) {
                        connection.setRequestProperty("Range", "bytes=$currentBytesForResumeHeader-")
                    }
                }

                connection.connectTimeout = 15000
                connection.readTimeout = 30000
                connection.connect()

                val responseCode = connection.responseCode
                if (responseCode != HttpURLConnection.HTTP_OK && responseCode != HttpURLConnection.HTTP_PARTIAL) {
                    updateDownloadStatus(item, DownloadStatus.FAILED)
                    return@launch
                }

                // Determine actual filename from headers
                val contentDisposition = connection.getHeaderField("Content-Disposition")
                val serverContentType = connection.contentType // More reliable than initial mimeType
                var finalFileName = item.fileName // Start with current (cleaned initial)

                if (item.source != "JSON") { // Only derive from server if not a JSON-sourced download
                    var serverSuggestedName: String? = null
                    var preferRFC5987 = false
                    if (contentDisposition != null) {
                    val parts = contentDisposition.split(';')
                    for (partValue in parts) {
                        val part = partValue.trim()
                        if (part.startsWith("filename*=", ignoreCase = true)) {
                            val value = part.substring("filename*=".length)
                            val valueParts = value.split('\'', limit = 3)
                            if (valueParts.size == 3) {
                                val charset = valueParts[0]
                                val encodedName = valueParts[2]
                                try {
                                    serverSuggestedName = URLDecoder.decode(encodedName, if (charset.isBlank()) StandardCharsets.UTF_8.name() else charset)
                                    preferRFC5987 = true
                                    break
                                } catch (e: Exception) {
                                    serverSuggestedName = encodedName // Store raw as fallback
                                }
                            } else if (serverSuggestedName == null) {
                                 serverSuggestedName = value.removeSurrounding("\"")
                            }
                        } else if (part.startsWith("filename=", ignoreCase = true) && !preferRFC5987) {
                            var tempName = part.substring("filename=".length).removeSurrounding("\"")
                            if (tempName.contains("%")) { // Try to decode if it looks URL encoded
                                try {
                                    tempName = URLDecoder.decode(tempName, StandardCharsets.UTF_8.name())
                                } catch (e: Exception) { /* keep as is */ }
                            }
                            serverSuggestedName = tempName
                        }
                    }
                }

                if (serverSuggestedName != null) {
                    finalFileName = getCleanFileName(serverSuggestedName, serverContentType ?: item.mimeType)
                } else {
                    // Fallback: try to use URL path if no Content-Disposition
                    val urlPath = URL(item.url).path
                    val nameFromUrl = File(urlPath).name
                    if (nameFromUrl.isNotEmpty()) {
                        val tempName = getCleanFileName(nameFromUrl, serverContentType ?: item.mimeType)
                        // Avoid using a less specific name if item.fileName was already good (not default)
                        if (!item.fileName.startsWith("download_") && tempName.endsWith(".bin") && !item.fileName.endsWith(".bin")) {
                            // item.fileName is likely better
                        } else {
                            finalFileName = tempName
                        }
                    }
                }
                // If filename is still generic (e.g. "download_timestamp") and we have a server content type, refine it.
                if (finalFileName.startsWith("download_") && serverContentType != null) {
                    val baseNameForGeneric = finalFileName.substringBeforeLast('.', finalFileName)
                    finalFileName = getCleanFileName(baseNameForGeneric, serverContentType)
                }
            } // End of if (item.source != "JSON")

                // If filename changed, update DownloadItem and target file path
                if (finalFileName != item.fileName) {
                    val oldFilePathObj = File(item.filePath)
                    val newFilePath = File(oldFilePathObj.parentFile, finalFileName).absolutePath

                    item.fileName = finalFileName
                    item.filePath = newFilePath // This is the path we will download to
                    if (oldFilePathObj.exists() && oldFilePathObj.absolutePath != newFilePath) {
                    }

                    updateNotification(item) // Filename in notification might change
                    saveDownloadState()    // Persist new filename/path
                }

                val targetFile = File(item.filePath) // Use the final, potentially updated, filePath

                var currentBytes = 0L
                if (targetFile.exists() && item.status != DownloadStatus.CANCELLED && item.status != DownloadStatus.FAILED) {
                    currentBytes = targetFile.length()
                    if (currentBytes > 0) {
                        item.downloadedBytes = currentBytes
                    } else {
                        if (targetFile.exists()) targetFile.delete() // Start fresh if file exists but size is 0
                        item.downloadedBytes = 0L
                    }
                } else {
                     item.downloadedBytes = 0L // Ensure it's reset if no existing file
                }

                // Check response code again, as it dictates how to handle content length and stream
                if ((responseCode == HttpURLConnection.HTTP_OK && item.downloadedBytes == 0L) || // Fresh download
                    (responseCode == HttpURLConnection.HTTP_PARTIAL && item.downloadedBytes > 0)) { // Resuming download

                    val contentLengthHeader = try {
                        val headerValue = connection.getHeaderField("Content-Length")
                        headerValue?.toLongOrNull() ?: -1L
                    } catch (e: Exception) {
                        -1L
                    }

                    if (item.totalBytes == -1L || responseCode == HttpURLConnection.HTTP_OK) {
                        item.totalBytes = if (contentLengthHeader > 0) {
                            if (responseCode == HttpURLConnection.HTTP_PARTIAL) {
                                item.downloadedBytes + contentLengthHeader
                            } else {
                                contentLengthHeader
                            }
                        } else {
                            -1L
                        }
                    }

                    inputStream = connection.inputStream
                    outputStream = FileOutputStream(targetFile, item.downloadedBytes > 0) // Append if resuming
                    val buffer = ByteArray(8 * 1024)
                    var bytesRead: Int

                    while (isActive && item.status == DownloadStatus.DOWNLOADING) {
                        bytesRead = inputStream.read(buffer)
                        if (bytesRead == -1) break

                        outputStream.write(buffer, 0, bytesRead)
                        item.downloadedBytes += bytesRead
                        updateDownloadProgress(item)
                    }
                    outputStream.flush()

                    if (item.status == DownloadStatus.DOWNLOADING) {
                        if (item.totalBytes != -1L && item.downloadedBytes >= item.totalBytes) {
                            updateDownloadStatus(item, DownloadStatus.COMPLETED)
                            // Reset retry count on successful completion
                            retryAttempts.remove(item.id)
                            failedDownloads.remove(item.id)
                        } else if (item.totalBytes == -1L && item.downloadedBytes > 0) { // If size unknown but got data
                            updateDownloadStatus(item, DownloadStatus.COMPLETED)
                            // Reset retry count on successful completion
                            retryAttempts.remove(item.id)
                            failedDownloads.remove(item.id)
                        } else if (item.downloadedBytes == 0L && item.totalBytes <=0L) { // No data and unknown size
                             updateDownloadStatus(item, DownloadStatus.FAILED) // Likely an issue
                        }
                        else {
                            updateDownloadStatus(item, DownloadStatus.FAILED) // Or PAUSED if interruption
                        }
                    }
                } else if (responseCode == HttpURLConnection.HTTP_OK && item.downloadedBytes > 0) {
                    // Server responded with 200 OK even though we tried to resume (had downloadedBytes > 0).
                    // This implies the server doesn't support resume for this request or is sending the full file.
                    // We should restart the download from the beginning for this item.
                    Log.w(TAG, "Server sent 200 OK for a resume request for ${item.fileName}. Restarting download from byte 0.")
                    item.downloadedBytes = 0L
                    if (targetFile.exists()) {
                        try {
                            targetFile.delete()
                        } catch (e: Exception) {
                            Log.e(TAG, "Error deleting file before restart: ${targetFile.absolutePath}", e)
                        }
                    }

                    // Update totalBytes from the new 200 OK response.
                    val newContentLengthHeader = try {
                        val headerValue = connection.getHeaderField("Content-Length")
                        headerValue?.toLongOrNull() ?: -1L
                    } catch (e: Exception) { -1L }
                    item.totalBytes = newContentLengthHeader

                    // Close previous stream and reopen in overwrite mode.
                    try {
                        outputStream?.close()
                    } catch (e: IOException) { Log.e(TAG, "Error closing stream before restart: ${e.message}") }
                    outputStream = FileOutputStream(targetFile, false) // false for overwrite

                    // The status remains DOWNLOADING. The download loop will continue with 0 bytes.
                    // Progress will be updated accordingly. No FAILED status transition.
                }
                else {
                    updateDownloadStatus(item, DownloadStatus.FAILED)
                }

            } catch (e: Exception) {
                if (isActive && item.status == DownloadStatus.DOWNLOADING) {
                    Log.e(TAG, "Download exception for ${item.fileName}: ${e.message}", e)

                    if (isNetworkAvailable) {
                        // Network is fine, but download failed (e.g., server error, bad URL)
                        Log.d(TAG, "Download failed (network OK): ${item.fileName}. Marking for retry.")
                        updateDownloadStatus(item, DownloadStatus.FAILED)
                        failedDownloads[item.id] = System.currentTimeMillis()
                    } else {
                        // Network became unavailable during this download attempt or was already unavailable
                        Log.d(TAG, "Download failed (network UNAVAILABLE): ${item.fileName}. Pausing.")
                        updateDownloadStatus(item, DownloadStatus.PAUSED)
                        // The job is ending due to this exception.
                        // The onAvailable callback will handle resuming when network is back.
                    }
                } else if (item.status == DownloadStatus.PAUSED) {
                    Log.d(TAG, "Download for ${item.fileName} was already paused during exception handling. No status change.")
                } else {
                    Log.d(TAG, "Download exception for ${item.fileName}, but job not active or status not DOWNLOADING. Current status: ${item.status}, isActive: $isActive")
                }
            } finally {
                try {
                    outputStream?.flush()
                    outputStream?.close()
                    inputStream?.close()
                    connection?.disconnect()
                } catch (e: IOException) {
                    Log.e(TAG, "Error closing streams: ${e.message}")
                }
                if (item.status != DownloadStatus.PAUSED && item.status != DownloadStatus.DOWNLOADING) {
                    downloadJobs.remove(item.id)
                }
                // Foreground and queue checks are handled by updateDownloadStatus
            }
        }
        downloadJobs[item.id] = job
    }

    private fun handlePauseDownload(intent: Intent) {
        val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
        if (downloadId == -1L) return

        downloadItems[downloadId]?.let { item ->
            if (item.status == DownloadStatus.DOWNLOADING) {
                updateDownloadStatus(item, DownloadStatus.PAUSED)
                downloadJobs[downloadId]?.cancel(CancellationException("Download paused by user"))
            }
        }
    }

    private fun handleResumeDownload(intent: Intent) {
        val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
        if (downloadId == -1L) return

        downloadItems[downloadId]?.let { item ->
            if (item.status == DownloadStatus.PAUSED) {
                // Reset failure count on manual resume
                item.failureCount = 0
                Log.d(TAG, "Manual resume: reset failure count for ${item.fileName}")
                startOrQueueDownload(item) // This will set to PENDING then DOWNLOADING
            }
        }
    }

    private fun handleCancelDownload(intent: Intent) {
        val downloadId = intent.getLongExtra(EXTRA_DOWNLOAD_ID, -1)
        if (downloadId == -1L) return

        downloadItems[downloadId]?.let { item ->
            val oldStatus = item.status
            updateDownloadStatus(item, DownloadStatus.CANCELLED) // This handles JSON counter decrement if needed
            downloadJobs.remove(downloadId)?.cancel(CancellationException("Download cancelled by user"))

            // Clean up retry tracking
            retryAttempts.remove(downloadId)
            failedDownloads.remove(downloadId)

            try {
                val file = File(item.filePath)
                if (file.exists()) file.delete()
            } catch (e: Exception) { /* Log error */ }

            downloadItems.remove(downloadId) // Remove after status update and file deletion attempt
            saveDownloadState() // Save state after removing item
            notificationManager.cancel(item.id.toInt()) // Explicitly cancel notification
            // UI update broadcast is sent by updateDownloadStatus
        }
    }

    private fun updateDownloadStatus(item: DownloadItem, newStatus: DownloadStatus) {
        val oldStatus = item.status
        if (oldStatus == newStatus && newStatus != DownloadStatus.DOWNLOADING) return // Avoid redundant updates unless it's progress

        item.status = newStatus
        var listChanged = false

        // Handle failure count increment when transitioning to FAILED
        if (newStatus == DownloadStatus.FAILED && oldStatus != DownloadStatus.FAILED) {
            item.failureCount++
            Log.d(TAG, "Download ${item.fileName} failed (count: ${item.failureCount}/${MAX_FAILURE_COUNT})")
        }

        // Reset failure count on successful completion or manual cancellation
        if (newStatus == DownloadStatus.COMPLETED || newStatus == DownloadStatus.CANCELLED) {
            item.failureCount = 0
        }

        if (item.source == "JSON") {
            if ((oldStatus == DownloadStatus.DOWNLOADING || oldStatus == DownloadStatus.PENDING) &&
                (newStatus == DownloadStatus.COMPLETED || newStatus == DownloadStatus.FAILED || newStatus == DownloadStatus.CANCELLED || newStatus == DownloadStatus.PAUSED)) {
                activeJsonDownloads.decrementAndGet().coerceAtLeast(0) // Ensure not negative
            }
        }

        if (newStatus == DownloadStatus.PENDING || newStatus == DownloadStatus.CANCELLED || oldStatus == DownloadStatus.PENDING && newStatus != DownloadStatus.PENDING) {
            listChanged = true // List changes on add, remove, or moving from pending
        }

        sendUpdateBroadcast(item.id, listChanged)
        updateNotification(item)
        saveDownloadState()

        if (newStatus == DownloadStatus.DOWNLOADING) {
            checkStartForeground()
        } else if (newStatus == DownloadStatus.COMPLETED || newStatus == DownloadStatus.FAILED || newStatus == DownloadStatus.CANCELLED || newStatus == DownloadStatus.PAUSED) {
            checkAndStartPendingJsonDownloads() // A slot might have opened up
            checkStopForeground()
        }
    }

    private fun updateDownloadProgress(item: DownloadItem) {
        sendUpdateBroadcast(item.id)
        updateNotification(item)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Download Service Channel"
            val descriptionText = "Notifications for file downloads"
            val importance = NotificationManager.IMPORTANCE_LOW
            val channel = NotificationChannel(notificationChannelId, name, importance).apply {
                description = descriptionText
            }
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun createForegroundNotification(): Notification {
        val openAppIntent = Intent(this, DownloadActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
        }
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            this, 0, openAppIntent, pendingIntentFlags
        )

        return NotificationCompat.Builder(this, notificationChannelId)
            .setContentTitle(getString(R.string.app_name))
            .setContentText("Downloads in progress...")
            .setSmallIcon(R.drawable.ic_download)
            .setContentIntent(pendingIntent)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setOngoing(true)
            .build()
    }

    private fun updateNotification(item: DownloadItem) {
        val notificationBuilder = NotificationCompat.Builder(this, notificationChannelId)
            .setSmallIcon(R.drawable.ic_download)
            .setContentTitle(item.fileName) // Use the (potentially updated) filename
            .setOnlyAlertOnce(true)

        val openAppIntent = Intent(this, DownloadActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT
        }
        val pendingIntentFlags = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
        } else {
            PendingIntent.FLAG_UPDATE_CURRENT
        }
        val contentPendingIntent: PendingIntent = PendingIntent.getActivity(
            this, item.id.toInt(), openAppIntent, pendingIntentFlags
        )
        notificationBuilder.setContentIntent(contentPendingIntent)

        // Add Cancel Action for appropriate states
        if (item.status == DownloadStatus.DOWNLOADING || item.status == DownloadStatus.PENDING || item.status == DownloadStatus.PAUSED) {
            val cancelIntent = createControlIntent(this, ACTION_CANCEL_DOWNLOAD, item.id)
            val cancelPendingIntent: PendingIntent = PendingIntent.getService(
                this,
                item.id.toInt() + 1000, // Unique request code for cancel action
                cancelIntent,
                pendingIntentFlags
            )
            notificationBuilder.addAction(R.drawable.ic_cancel, "Cancel", cancelPendingIntent)
        }

        when (item.status) {
            DownloadStatus.DOWNLOADING -> {
                val progress = item.progress
                val totalMb = if (item.totalBytes > 0) String.format("%.2f MB", item.totalBytes / (1024.0 * 1024.0)) else "Unknown size"
                val downloadedMb = String.format("%.2f MB", item.downloadedBytes / (1024.0 * 1024.0))
                val contentText = "Downloading: $downloadedMb / $totalMb (${progress}%)"
                notificationBuilder
                    .setContentText(contentText)
                    .setStyle(NotificationCompat.BigTextStyle().bigText(contentText))
                    .setProgress(100, progress, item.totalBytes <= 0)
                    .setOngoing(true)
                    .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            }
            DownloadStatus.PENDING -> {
                notificationBuilder
                    .setContentText("Waiting to download...")
                    .setStyle(NotificationCompat.BigTextStyle().bigText("Waiting to download..."))
                    .setProgress(0, 0, true)
                    .setOngoing(true)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
            }
            DownloadStatus.PAUSED -> {
                val contentText = "Paused - ${item.progress}%"
                notificationBuilder
                    .setContentText(contentText)
                    .setStyle(NotificationCompat.BigTextStyle().bigText(contentText))
                    .setProgress(100, item.progress, false)
                    .setOngoing(false)
                    .setPriority(NotificationCompat.PRIORITY_LOW)
            }
            DownloadStatus.COMPLETED -> {
                notificationManager.cancel(item.id.toInt()) // Cancel ongoing first
                val file = File(item.filePath)
                val fileUri: Uri? = try {
                    FileProvider.getUriForFile(this, "${applicationContext.packageName}.provider", file)
                } catch (e: IllegalArgumentException) { null }

                val viewIntent = Intent(Intent.ACTION_VIEW)
                if (fileUri != null) {
                    val mime = item.mimeType ?: contentResolver.getType(fileUri) ?: "*/*"
                    viewIntent.setDataAndType(fileUri, mime)
                    viewIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                } else { // Fallback to opening DownloadActivity
                    viewIntent.setClass(this, DownloadActivity::class.java)
                }
                viewIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

                val viewPendingIntent: PendingIntent = PendingIntent.getActivity(
                    this, item.id.toInt(), viewIntent, pendingIntentFlags
                )
                val completedNotification = NotificationCompat.Builder(this, notificationChannelId)
                    .setSmallIcon(R.drawable.ic_download)
                    .setContentTitle(item.fileName)
                    .setContentText("Download complete - Tap to open")
                    .setStyle(NotificationCompat.BigTextStyle().bigText("Download complete - Tap to open"))
                    .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                    .setContentIntent(viewPendingIntent)
                    .setAutoCancel(true)
                    .build()
                notificationManager.notify(item.id.toInt(), completedNotification)
                return
            }
            DownloadStatus.FAILED -> {
                notificationManager.cancel(item.id.toInt()) // Cancel ongoing first

                val failedMessage = if (item.failureCount < MAX_FAILURE_COUNT) {
                    if (item.downloadedBytes > 0) {
                        "Download paused (${item.progress}%) - Will auto-retry (${item.failureCount}/${MAX_FAILURE_COUNT})"
                    } else {
                        "Download failed - Will auto-retry (${item.failureCount}/${MAX_FAILURE_COUNT})"
                    }
                } else {
                    "Download failed ${MAX_FAILURE_COUNT} times - Will be paused for manual retry"
                }

                val failedNotification = NotificationCompat.Builder(this, notificationChannelId)
                    .setSmallIcon(R.drawable.ic_download)
                    .setContentTitle(item.fileName)
                    .setContentText(failedMessage)
                    .setStyle(NotificationCompat.BigTextStyle().bigText(failedMessage))
                    .setPriority(NotificationCompat.PRIORITY_LOW)
                    .setContentIntent(contentPendingIntent) // To open app
                    .setAutoCancel(true)
                    .build()
                notificationManager.notify(item.id.toInt(), failedNotification)
                return
            }
            DownloadStatus.CANCELLED -> {
                notificationManager.cancel(item.id.toInt())
                return
            }
        }
        notificationManager.notify(item.id.toInt(), notificationBuilder.build())
    }

    inner class DownloadBinder : Binder() {
        fun getService(): DownloadService = this@DownloadService
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onDestroy() {
        super.onDestroy()
        serviceJob.cancel(CancellationException("DownloadService destroyed"))
        notificationManager.cancelAll()

        // Unregister network callback
        networkCallback?.let {
            try {
                connectivityManager.unregisterNetworkCallback(it)
                networkCallback = null
            } catch (e: Exception) {
                Log.e(TAG, "Error unregistering network callback: ${e.message}")
            }
        }
    }

    @Synchronized
    private fun saveDownloadState() {
        try {
            val itemsToSave = downloadItems.values.toList()
            val jsonString = gson.toJson(itemsToSave)
            val lastId = downloadIdCounter.get()
            sharedPreferences.edit()
                .putString(KEY_DOWNLOAD_ITEMS, jsonString)
                .putLong(KEY_LAST_DOWNLOAD_ID, lastId)
                .apply()
        } catch (e: Exception) { /* Log error */ }
    }

    @Synchronized
    private fun loadDownloadState() {
        try {
            val jsonString = sharedPreferences.getString(KEY_DOWNLOAD_ITEMS, null)
            val lastId = sharedPreferences.getLong(KEY_LAST_DOWNLOAD_ID, 0L)
            downloadIdCounter.set(lastId)

            if (jsonString != null) {
                val type = object : TypeToken<List<DownloadItem>>() {}.type
                val loadedItems: List<DownloadItem> = gson.fromJson(jsonString, type)
                downloadItems.clear()
                retryAttempts.clear()
                var maxIdFound = 0L
                loadedItems.forEach { item ->
                    if (item.status == DownloadStatus.DOWNLOADING || item.status == DownloadStatus.PENDING) {
                        item.status = DownloadStatus.PAUSED // Treat as paused on restart
                    }
                    downloadItems[item.id] = item
                    // Reset retry attempts on service restart
                    retryAttempts[item.id] = 0
                    if (item.id > maxIdFound) maxIdFound = item.id
                }
                if (downloadIdCounter.get() <= maxIdFound) {
                    downloadIdCounter.set(maxIdFound)
                }
                sendUpdateBroadcast(-1, listChanged = true)
            } else {
                 downloadIdCounter.set(System.currentTimeMillis() / 1000) // Fallback if no state
            }
        } catch (e: Exception) {
            downloadIdCounter.set(System.currentTimeMillis() / 1000) // Fallback on error
        }
    }

    fun getDownloadItems(): List<DownloadItem> {
        return downloadItems.values.map { it.copy() }.sortedWith(
            compareBy<DownloadItem> {
                when (it.status) {
                    DownloadStatus.DOWNLOADING -> 0
                    DownloadStatus.PENDING -> 1
                    else -> 2
                }
            }.thenByDescending { it.createdAt }
        )
    }

    fun getDownloadItem(id: Long): DownloadItem? {
        return downloadItems[id]?.copy()
    }

    private fun sendUpdateBroadcast(downloadId: Long, listChanged: Boolean = false) {
        val intent = Intent(ACTION_DOWNLOAD_UPDATE).apply {
            putExtra(EXTRA_DOWNLOAD_ID_UPDATE, downloadId)
            putExtra(EXTRA_DOWNLOAD_LIST_CHANGED, listChanged)
        }
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent)
    }

    @Synchronized
    private fun checkAndStartPendingJsonDownloads() {
        if (activeJsonDownloads.get() < MAX_CONCURRENT_JSON_DOWNLOADS) {
            val nextDownload = downloadItems.values
                .filter { it.source == "JSON" && it.status == DownloadStatus.PENDING }
                .minByOrNull { it.createdAt }
            nextDownload?.let {
                startOrQueueDownload(it) // This will increment counter and start
            }
        }
    }

    @Synchronized
    private fun checkStopForeground() {
        val hasActiveDownloads = downloadItems.values.any { it.status == DownloadStatus.DOWNLOADING }
        if (!hasActiveDownloads && isForeground) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                stopForeground(STOP_FOREGROUND_REMOVE)
            } else {
                @Suppress("DEPRECATION")
                stopForeground(true)
            }
            isForeground = false
        }
    }

    @Synchronized
    private fun checkStartForeground() {
        val hasActiveDownloads = downloadItems.values.any { it.status == DownloadStatus.DOWNLOADING }
        if (hasActiveDownloads && !isForeground) {
            try {
                startForeground(foregroundNotificationId, createForegroundNotification())
                isForeground = true
            } catch (e: Exception) { /* Log error, e.g. background start restrictions */ }
        }
    }
}

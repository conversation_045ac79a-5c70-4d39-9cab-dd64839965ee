package com.elewashy.egyfilm.util

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class PermissionManager(private val activity: AppCompatActivity) {

    companion object {
        private const val STORAGE_PERMISSION_REQUEST_CODE = 100
        private const val MANAGE_EXTERNAL_STORAGE_REQUEST_CODE = 101
    }

    // Permission launchers
    private lateinit var requestPermissionsLauncher: ActivityResultLauncher<Array<String>>
    private lateinit var manageExternalStorageLauncher: ActivityResultLauncher<Intent>
    private lateinit var installPackagesLauncher: ActivityResultLauncher<Intent>

    // Callbacks
    private var onPermissionGranted: (() -> Unit)? = null
    private var onPermissionDenied: (() -> Unit)? = null

    init {
        setupPermissionLaunchers()
    }

    private fun setupPermissionLaunchers() {
        // For regular permissions (Android 6-10)
        requestPermissionsLauncher = activity.registerForActivityResult(
            ActivityResultContracts.RequestMultiplePermissions()
        ) { permissions ->
            val allGranted = permissions.values.all { it }
            if (allGranted) {
                onPermissionGranted?.invoke()
            } else {
                handlePermissionDenied()
            }
        }

        // For manage external storage (Android 11+)
        manageExternalStorageLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                if (Environment.isExternalStorageManager()) {
                    onPermissionGranted?.invoke()
                } else {
                    handlePermissionDenied()
                }
            }
        }

        // For install packages permission (Android 8+)
        installPackagesLauncher = activity.registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (activity.packageManager.canRequestPackageInstalls()) {
                    onPermissionGranted?.invoke()
                } else {
                    onPermissionDenied?.invoke()
                }
            }
        }
    }

    fun requestStoragePermission(
        onGranted: () -> Unit,
        onDenied: () -> Unit = {}
    ) {
        this.onPermissionGranted = onGranted
        this.onPermissionDenied = onDenied

        when {
            hasStoragePermission() -> {
                onGranted()
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                // Android 11+ - Need MANAGE_EXTERNAL_STORAGE
                requestManageExternalStoragePermission()
            }
            else -> {
                // Android 6-10 - Need READ/WRITE_EXTERNAL_STORAGE
                requestLegacyStoragePermissions()
            }
        }
    }

    private fun hasStoragePermission(): Boolean {
        return when {
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.R -> {
                Environment.isExternalStorageManager()
            }
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                ContextCompat.checkSelfPermission(
                    activity,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ) == PackageManager.PERMISSION_GRANTED
            }
            else -> true // Permissions granted at install time for older versions
        }
    }

    private fun requestManageExternalStoragePermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            showPermissionExplanationDialog(
                title = "Storage Permission Required",
                message = "This app needs access to device storage to download and manage files. Please grant 'All files access' permission in the next screen.",
                positiveAction = {
                    try {
                        val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                        intent.data = Uri.parse("package:${activity.packageName}")
                        manageExternalStorageLauncher.launch(intent)
                    } catch (e: Exception) {
                        // Fallback to general manage external storage settings
                        val intent = Intent(Settings.ACTION_MANAGE_ALL_FILES_ACCESS_PERMISSION)
                        manageExternalStorageLauncher.launch(intent)
                    }
                }
            )
        }
    }

    private fun requestLegacyStoragePermissions() {
        val permissions = arrayOf(
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        )

        val shouldShowRationale = permissions.any { 
            ActivityCompat.shouldShowRequestPermissionRationale(activity, it)
        }

        if (shouldShowRationale) {
            showPermissionExplanationDialog(
                title = "Storage Permission Required",
                message = "This app needs access to device storage to download and save files. Please grant storage permission to continue.",
                positiveAction = {
                    requestPermissionsLauncher.launch(permissions)
                }
            )
        } else {
            requestPermissionsLauncher.launch(permissions)
        }
    }

    private fun handlePermissionDenied() {
        showPermissionDeniedDialog()
    }

    private fun showPermissionExplanationDialog(
        title: String,
        message: String,
        positiveAction: () -> Unit
    ) {
        AlertDialog.Builder(activity)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("Grant Permission") { _, _ ->
                positiveAction()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
                onPermissionDenied?.invoke()
            }
            .setCancelable(false)
            .show()
    }

    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(activity)
            .setTitle("Permission Required")
            .setMessage("Storage permission is required for this app to function properly. You can grant it from App Settings.")
            .setPositiveButton("Go to Settings") { _, _ ->
                openAppSettings()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
                onPermissionDenied?.invoke()
            }
            .setCancelable(false)
            .show()
    }

    private fun openAppSettings() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            intent.data = Uri.parse("package:${activity.packageName}")
            activity.startActivity(intent)
        } catch (e: Exception) {
            val intent = Intent(Settings.ACTION_SETTINGS)
            activity.startActivity(intent)
        }
    }

    // Check install packages permission (Android 8+)
    fun requestInstallPackagesPermission(
        onGranted: () -> Unit = {},
        onDenied: () -> Unit = {}
    ) {
        this.onPermissionGranted = onGranted
        this.onPermissionDenied = onDenied

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            when {
                activity.packageManager.canRequestPackageInstalls() -> {
                    onGranted()
                }
                else -> {
                    showPermissionExplanationDialog(
                        title = "Install Apps Permission Required",
                        message = "This app needs permission to install APK files. This allows you to install downloaded APK applications directly from the app.",
                        positiveAction = {
                            try {
                                val intent = Intent(Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES)
                                intent.data = Uri.parse("package:${activity.packageName}")
                                installPackagesLauncher.launch(intent)
                            } catch (e: Exception) {
                                onPermissionDenied?.invoke()
                            }
                        }
                    )
                }
            }
        } else {
            onGranted() // Install packages permission is automatically granted on older versions
        }
    }

    // Check notification permission (Android 13+)
    fun requestNotificationPermission(
        onGranted: () -> Unit = {},
        onDenied: () -> Unit = {}
    ) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            when {
                ContextCompat.checkSelfPermission(
                    activity,
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED -> {
                    onGranted()
                }
                ActivityCompat.shouldShowRequestPermissionRationale(
                    activity,
                    Manifest.permission.POST_NOTIFICATIONS
                ) -> {
                    showPermissionExplanationDialog(
                        title = "Notification Permission",
                        message = "This app would like to send you notifications about download progress and completion.",
                        positiveAction = {
                            val notificationLauncher = activity.registerForActivityResult(
                                ActivityResultContracts.RequestPermission()
                            ) { granted ->
                                if (granted) onGranted() else onDenied()
                            }
                            notificationLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                        }
                    )
                }
                else -> {
                    val notificationLauncher = activity.registerForActivityResult(
                        ActivityResultContracts.RequestPermission()
                    ) { granted ->
                        if (granted) onGranted() else onDenied()
                    }
                    notificationLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
            }
        } else {
            onGranted() // Notifications are automatically granted on older versions
        }
    }

    // Check if all required permissions are granted
    fun hasAllRequiredPermissions(): Boolean {
        val hasStorage = hasStoragePermission()
        val hasNotification = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
        val hasInstallPackages = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            activity.packageManager.canRequestPackageInstalls()
        } else {
            true
        }
        
        return hasStorage && hasNotification && hasInstallPackages
    }

    // Request all required permissions at once
    fun requestAllRequiredPermissions(
        onAllGranted: () -> Unit,
        onSomeOrAllDenied: () -> Unit = {}
    ) {
        requestStoragePermission(
            onGranted = {
                requestNotificationPermission(
                    onGranted = {
                        requestInstallPackagesPermission(
                            onGranted = onAllGranted,
                            onDenied = onSomeOrAllDenied
                        )
                    },
                    onDenied = onSomeOrAllDenied
                )
            },
            onDenied = onSomeOrAllDenied
        )
    }
}

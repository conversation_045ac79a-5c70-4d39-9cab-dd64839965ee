<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android"
    android:ordering="together">
    
    <!-- Rotation animation -->
    <objectAnimator
        android:propertyName="rotation"
        android:valueFrom="0"
        android:valueTo="360"
        android:valueType="floatType"
        android:duration="1200"
        android:interpolator="@android:anim/linear_interpolator"
        android:repeatCount="infinite"
        android:repeatMode="restart" />
    
    <!-- Primary arc animation -->
    <objectAnimator
        android:propertyName="trimPathStart"
        android:valueFrom="0"
        android:valueTo="0.8"
        android:valueType="floatType"
        android:duration="1000"
        android:interpolator="@android:interpolator/fast_out_slow_in"
        android:repeatCount="infinite"
        android:repeatMode="reverse"
        android:startOffset="0" />
        
    <objectAnimator
        android:propertyName="trimPathEnd"
        android:valueFrom="0.3"
        android:valueTo="1"
        android:valueType="floatType"
        android:duration="1000"
        android:interpolator="@android:interpolator/fast_out_slow_in"
        android:repeatCount="infinite"
        android:repeatMode="reverse"
        android:startOffset="200" />
        
</set>
<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/egyfilm_background"
    tools:context=".activity.DownloadActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/egyfilm_background"
        android:elevation="0dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar_downloads"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/egyfilm_background"
            android:elevation="0dp"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:titleTextColor="@color/white"
            app:navigationIconTint="@color/white" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/egyfilm_gray"
            android:alpha="0.3" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_downloads"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:paddingTop="8dp"
            android:paddingBottom="16dp"
            android:clipToPadding="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:listitem="@layout/item_download" />

        <LinearLayout
            android:id="@+id/layout_no_downloads"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <ImageView
                android:layout_width="72dp"
                android:layout_height="72dp"
                android:src="@drawable/ic_download_file"
                android:alpha="0.3"
                app:tint="@color/egyfilm_gray" />

            <TextView
                android:id="@+id/tv_no_downloads"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="No Downloads"
                android:textAppearance="?attr/textAppearanceHeadline6"
                android:textColor="@color/gray_text" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Your downloads will appear here"
                android:textAppearance="?attr/textAppearanceBody2"
                android:textColor="@color/egyfilm_gray"
                android:alpha="0.7" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>

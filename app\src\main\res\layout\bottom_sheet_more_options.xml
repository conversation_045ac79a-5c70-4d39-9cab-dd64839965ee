<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/egyfilm_dark_gray"
    android:padding="16dp">

    <!-- Optional: Add a handle for the bottom sheet -->
    <View
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="12dp"
        android:background="@drawable/search_bar_background" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/backBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:icon="@drawable/ic_back"
            android:text="@string/back"
            app:iconGravity="top"
            android:backgroundTint="@android:color/transparent"
            app:iconTint="@color/white"
            android:textColor="@color/egyfilm_gray"
            android:textSize="12sp"
            android:padding="12dp"
            app:cornerRadius="20dp"
            android:textAllCaps="false"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/forwardBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:icon="@drawable/ic_forward"
            android:text="@string/forward"
            app:iconGravity="top"
            android:backgroundTint="@android:color/transparent"
            app:iconTint="@color/white"
            android:textColor="@color/egyfilm_gray"
            android:textSize="12sp"
            android:padding="12dp"
            app:cornerRadius="20dp"
            android:textAllCaps="false"/>

        <!-- Downloads Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/downloadsBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:icon="@drawable/ic_download"
            android:text="@string/downloads"
            app:iconGravity="top"
            android:backgroundTint="@android:color/transparent"
            app:iconTint="@color/white"
            android:textColor="@color/egyfilm_gray"
            android:textSize="12sp"
            android:padding="12dp"
            app:cornerRadius="20dp"
            android:textAllCaps="false"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp"
        android:gravity="center">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/fullscreenBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:icon="@drawable/ic_fullscreen"
            android:text="@string/fullscreen"
            app:iconGravity="top"
            android:backgroundTint="@android:color/transparent"
            app:iconTint="@color/white"
            android:textColor="@color/egyfilm_gray"
            android:textSize="12sp"
            android:padding="12dp"
            app:cornerRadius="20dp"
            android:textAllCaps="false"/>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/settingsBtn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:icon="@drawable/ic_settings"
            android:text="@string/settings"
            app:iconGravity="top"
            android:backgroundTint="@android:color/transparent"
            app:iconTint="@color/white"
            android:textColor="@color/egyfilm_gray"
            android:textSize="12sp"
            android:padding="12dp"
            app:cornerRadius="20dp"
            android:textAllCaps="false"/>

    </LinearLayout>

</LinearLayout>

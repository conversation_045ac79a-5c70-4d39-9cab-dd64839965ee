<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".fragment.HomeFragment"
    android:background="@drawable/bg_gradient">

    <SearchView
        android:id="@+id/searchView"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3"
        android:queryHint="@string/search_hint"
        android:background="@drawable/custom_design"
        android:layout_marginHorizontal="24dp"
        android:iconifiedByDefault="false"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:layout_marginHorizontal="24dp"
        android:layout_marginTop="25dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/searchView"
        android:nestedScrollingEnabled="false"/>


    <TextView
        android:id="@+id/viewAllBtn"
        android:layout_width="100dp"
        android:layout_height="50dp"
        android:text="@string/view_all"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/recyclerView"
        android:textSize="16sp"
        android:textAlignment="center"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="10dp"/>


</androidx.constraintlayout.widget.ConstraintLayout>
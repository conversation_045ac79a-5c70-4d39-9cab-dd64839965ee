<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="egyfilmProgressStyle" parent="Widget.MaterialComponents.CircularProgressIndicator">
        <item name="indicatorSize">48dp</item>
        <item name="trackThickness">3dp</item>
        <item name="trackCornerRadius">2dp</item>
        <item name="indicatorColor">@color/egyfilm_red</item>
        <item name="trackColor">@android:color/transparent</item>
        <item name="indicatorDirectionCircular">clockwise</item>
        <item name="android:indeterminateOnly">true</item>
        <item name="android:indeterminateDuration">2500</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <!-- Show only quarter of the circle -->
        <item name="indicatorInset">0dp</item>
        <item name="minHideDelay">0</item>
    </style>
</resources>

<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.egyfilm" parent="Theme.MaterialComponents.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/egyfilm_red</item>
        <item name="colorPrimaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/egyfilm_red</item>
        <item name="colorSecondaryVariant">@color/egyfilm_red_dark</item>
        <item name="colorOnSecondary">@color/white</item>
        <!-- Background colors -->
        <item name="android:windowBackground">@color/egyfilm_black</item>
        <item name="backgroundColor">@color/egyfilm_black</item>
        <item name="colorSurface">@color/egyfilm_dark_gray</item>
        <item name="colorOnSurface">@color/white</item>
        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/egyfilm_gray</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/egyfilm_black</item>
        <!-- Navigation bar color -->
        <item name="android:navigationBarColor">@color/egyfilm_black</item>
        <!-- Customize your theme here. -->
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
    </style>

    <!-- Splash screen theme -->
    <style name="Theme.egyfilm.NoActionBar.Fullscreen" parent="Theme.egyfilm">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
    </style>

    <style name="roundCornerDialog" parent="ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="shapeAppearanceOverlay">@style/shape</item>
        <item name="android:background">@color/egyfilm_dark_gray</item>
        <item name="colorSurface">@color/egyfilm_dark_gray</item>
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:textColorSecondary">@color/egyfilm_gray</item>
    </style>

    <style name="shape">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>
</resources>
